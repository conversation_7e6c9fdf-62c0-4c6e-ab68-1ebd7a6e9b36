import 'dart:ffi' as ffi;

import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:ffi/ffi.dart';

import '../core/medias_kit_ffi.dart' as ffi;

/// Mask IPC管理器
/// 支持多个texture renderer，使用Ticker定时轮询C层数据
/// 优化版本：使用预分配1080P缓冲区，减少内存分配和拷贝
class MaskIpc implements TickerProvider {
  final String shmName;

  late final int _colorCount;

  late final ffi.Pointer<ffi.Uint32> _colorsPtr;

  ffi.MaskIpcRef? _maskIpcRef;

  /// 预分配的渲染帧缓冲区（1080P最大尺寸）
  final _frameBuffer = ffi.maskRenderFrameCreate(
    0,
    0,
    0,
    0,
    1920 * 1080 * 4,
    ffi.nullptr,
    0,
    0,
    1920 * 1080,
    ffi.nullptr,
    0,
  );

  Ticker? _ticker;

  final Map<String, ffi.TextureRendererRef> _renderers = {};

  var _isRunning = false;

  var _lastConnectionState = false;

  var _counter = 0;

  var showMask = true;

  /// 统计信息
  var _totalFramesProcessed = 0;

  /// 连接状态变化回调
  void Function(bool connected)? onConnectionChanged;

  /// 追踪状态
  void Function(bool isTracking)? onTrackingChanged;

  /// 构造函数
  ///
  /// - [shmName] 共享内存名称
  /// - [maskColors] 颜色数组，用于mask渲染, 至少需要一个颜色, 不超过20个
  MaskIpc(this.shmName, List<int> maskColors)
    : assert(maskColors.isNotEmpty && maskColors.length <= 20) {
    // 在构造函数中分配颜色数组内存
    _colorsPtr = malloc<ffi.Uint32>(maskColors.length);
    for (int i = 0; i < maskColors.length; i++) {
      _colorsPtr[i] = maskColors[i];
    }
    _colorCount = maskColors.length;
  }

  @override
  Ticker createTicker(TickerCallback onTick) {
    return Ticker(onTick);
  }

  /// 初始化IPC连接
  bool initialize() {
    if (_maskIpcRef != null) {
      return true; // 已初始化
    }

    final shmNamePtr = shmName.toNativeUtf8().cast<ffi.Char>();
    try {
      _maskIpcRef = ffi.maskIpcCreate(shmNamePtr);
      if (_maskIpcRef == ffi.nullptr) {
        return false;
      }
      return true;
    } finally {
      malloc.free(shmNamePtr);
    }
  }

  /// 启动定时轮询
  void start() {
    if (_isRunning || _maskIpcRef == null) {
      return;
    }

    _ticker = createTicker(_onTick);
    _ticker!.start();
    _isRunning = true;
  }

  /// 停止定时轮询
  void stop() {
    if (!_isRunning) {
      return;
    }

    _ticker?.stop();
    _ticker?.dispose();
    _ticker = null;
    _isRunning = false;
  }

  /// 添加texture renderer
  void addRenderer(String rendererId, ffi.TextureRendererRef renderer) {
    if (_renderers.containsKey(rendererId)) {
      debugPrint('Renderer $rendererId already exists, skipping...');
      return;
    }
    _renderers[rendererId] = renderer;
  }

  /// 移除texture renderer
  void removeRenderer(String rendererId) {
    _renderers.remove(rendererId);
  }

  /// Ticker回调 - 轮询C层数据
  void _onTick(Duration elapsed) {
    if (_maskIpcRef == null || !_isRunning) {
      return;
    }

    // 检查连接状态
    final currentConnectionState = ffi.maskIpcIsConnected(_maskIpcRef!);
    if (currentConnectionState != _lastConnectionState) {
      _lastConnectionState = currentConnectionState;
      onConnectionChanged?.call(currentConnectionState);
      debugPrint('MaskIpc connection state changed: $currentConnectionState');
    }

    if (!currentConnectionState) {
      debugPrint('MaskIpc connection lost, stopping...');
      stop();
      return;
    }

    // 检查是否有可用帧
    if (!ffi.maskIpcHasAvailableFrame(_maskIpcRef!)) {
      return;
    }

    // 尝试接收帧数据
    final isSuccess = ffi.maskIpcTryReceiveRenderFrameToBuffer(
      _maskIpcRef!,
      _frameBuffer,
    );
    if (!isSuccess) {
      return;
    }
    final frame = _frameBuffer.ref;

    _totalFramesProcessed++;

    if (frame.maskCount == 0) {
      if (_counter >= 0) {
        _counter--;
        if (_counter <= 0) {
          onTrackingChanged?.call(false);
        }
      }
    } else {
      if (_counter <= 0) {
        _counter = 60;
        onTrackingChanged?.call(true);
      }
    }

    // 分发给所有texture renderer
    for (final entry in _renderers.entries) {
      try {
        // 渲染原始图像数据到texture
        if (showMask) {
          ffi.textureRendererRenderRgbaFrameWithMask(
            entry.value,
            frame.imageData,
            frame.imageDataSize,
            frame.imageWidth,
            frame.imageHeight,
            frame.maskTextureData,
            frame.maskTextureWidth,
            frame.maskTextureHeight,
            _colorsPtr,
            _colorCount,
          );
        } else {
          ffi.textureRendererRenderRgbaFrame(
            entry.value,
            frame.imageData,
            frame.imageDataSize,
            frame.imageWidth,
            frame.imageHeight,
          );
        }
      } catch (e) {
        // 渲染器错误不应影响其他渲染器
        debugPrint('Renderer ${entry.key} error: $e');
      }
    }
  }

  /// 获取连接状态
  bool get isConnected {
    return _maskIpcRef != null && ffi.maskIpcIsConnected(_maskIpcRef!);
  }

  /// 获取可用帧数量
  int get availableFrameCount {
    return _maskIpcRef != null
        ? ffi.maskIpcGetAvailableFrameCount(_maskIpcRef!)
        : 0;
  }

  /// 获取已接收帧数
  int get framesReceived {
    return _maskIpcRef != null ? ffi.maskIpcGetFramesReceived(_maskIpcRef!) : 0;
  }

  /// 获取丢弃帧数
  int get framesDropped {
    return _maskIpcRef != null ? ffi.maskIpcGetFramesDropped(_maskIpcRef!) : 0;
  }

  /// 获取已处理帧数（Dart层统计）
  int get framesProcessed => _totalFramesProcessed;

  /// 获取运行状态
  bool get isRunning => _isRunning;

  /// 获取渲染器数量
  int get rendererCount => _renderers.length;

  /// 获取所有渲染器ID
  List<String> get rendererIds => _renderers.keys.toList();

  /// 获取指定渲染器的texture ID
  int? getTextureId(String rendererId) {
    final renderer = _renderers[rendererId];
    return renderer != null ? ffi.textureRendererGetTextureId(renderer) : null;
  }

  /// 销毁管理器
  void dispose() {
    stop();
    _renderers.clear();

    // 销毁预分配的渲染帧缓冲区
    ffi.maskRenderFrameDestroy(_frameBuffer);

    // 释放颜色数组内存
    malloc.free(_colorsPtr);

    // 销毁C层对象
    if (_maskIpcRef != null) {
      ffi.maskIpcDestroy(_maskIpcRef!);
      _maskIpcRef = null;
    }
  }

  @override
  String toString() {
    return 'MaskIpc{'
        'shmName: $shmName, '
        'connected: $isConnected, '
        'running: $isRunning, '
        'renderers: $rendererCount, '
        'received: $framesReceived, '
        'processed: $framesProcessed, '
        'dropped: $framesDropped, '
        'available: $availableFrameCount'
        '}';
  }
}
