#pragma once

#include <flutter_linux/flutter_linux.h>

#include <cstdint>
#include <mutex>
#include <string>
#include <vector>

#include "medias_kit_plugin_private.hpp"

namespace MK {

#define TEXTURE_GL_TYPE (texture_gl_get_type())

G_DECLARE_FINAL_TYPE(MKTextureGL, texture_gl, TEXTURE_GL, TEXTURE_GL, FlTextureGL)

#define TEXTURE_GL(obj) \
    (G_TYPE_CHECK_INSTANCE_CAST((obj), texture_gl_get_type(), MKTextureGL))

class TextureRenderer {

    friend void
    renderWithMask(MKTextureGL *self);

    friend gboolean
    texture_gl_populate_texture(
        FlTextureGL *texture,
        uint32_t *target,
        uint32_t *name,
        uint32_t *width,
        uint32_t *height,
        GError **error
    );

    const MediasKitPlugin &plugin;

    uint8_t *imageBuffer;

    // Mask 相关
    const uint8_t *maskBuffer = nullptr;
    uint32_t maskWidth = 0;
    uint32_t maskHeight = 0;
    bool maskEnabled = false;
    std::vector<uint32_t> tissueColors; // RGBA 颜色数组，最多支持20种颜色

    GdkGLContext *glContext = nullptr;

    MKTextureGL *texture = nullptr;

    int textureId;

    std::mutex mtx;

  public:
    TextureRenderer(const MediasKitPlugin &plugin);

    ~TextureRenderer();

    bool
    init();

    void
    renderRgbaFrame(
        const uint8_t *frame,
        const int64_t frameLen,
        const int64_t width,
        const int64_t height
    );

    // 重载版本：支持 mask 渲染
    void
    renderRgbaFrame(
        const uint8_t *frame,
        const int64_t frameLen,
        const int64_t width,
        const int64_t height,
        const uint8_t *maskData,
        const uint32_t maskWidth,
        const uint32_t maskHeight,
        const std::vector<uint32_t> &colors
    );

    /**
     * @brief 返回纹理对象的地址, 供 Flutter Texture 组件绑定
     */
    int64_t
    getTexture() const;
};
} // namespace MK