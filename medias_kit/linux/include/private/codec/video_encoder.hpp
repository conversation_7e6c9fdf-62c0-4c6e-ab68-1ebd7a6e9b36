#pragma once

#include <functional>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavutil/frame.h>
#include <libavutil/opt.h>
}

#include "types/types_ffi.h"

namespace MK {
class VideoEncoder {
  public:
    // Callback with (encoded_data, data_size, pts_ms, duration_ms, is_keyframe)
    typedef std::function<void(const uint8_t *, int, int64_t, int64_t, bool)> CallBack;

  private:
    AVCodecContext *codecContext = nullptr;
    AVFrame *avFrame = nullptr;
    AVPacket *avPacket = nullptr;

    const CallBack didEncode;

  public:
    const PixelFormat pixelFormat;
    const int width;
    const int height;
    const int framerate;
    const int bitrate;

    VideoEncoder(
        const PixelFormat pixelFormat,
        const int width,
        const int height,
        const int framerate,
        const int bitrate,
        const CallBack didEncode
    );

    ~VideoEncoder();

    bool init();

    bool encode(const uint8_t *const frame, long frameLen, int64_t pts_us);
};
} // namespace MK
