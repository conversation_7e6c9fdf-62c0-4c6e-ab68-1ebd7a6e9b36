#ifndef MASK_IPC_FFI_H
#define MASK_IPC_FFI_H

#include <stdbool.h>
#include <stdint.h>

#include "types/macros.h"

#ifdef __cplusplus
extern "C" {
#endif

/// MaskIpc管理器引用类型
typedef void *MaskIpcRef;

/// 渲染就绪的帧数据结构 - 已拷贝和解析完成，仅用于Dart层
typedef struct {
    /// 帧ID
    uint32_t frameId;

    /// 时间戳（毫秒）
    int64_t timestamp;

    /// 图像宽度
    uint32_t imageWidth;

    /// 图像高度
    uint32_t imageHeight;

    /// 原始图像数据大小
    uint32_t imageDataSize;

    /// 原始图像数据指针（RGBA格式，已拷贝）
    uint8_t *imageData;

    /// mask纹理宽度
    uint32_t maskTextureWidth;

    /// mask纹理高度
    uint32_t maskTextureHeight;

    /// mask纹理数据大小
    uint32_t maskTextureDataSize;

    /// 合并后的mask纹理数据指针（单通道，已解析）
    uint8_t *maskTextureData;

    /// 解码后的mask数量
    uint32_t maskCount;
} MaskRenderFrame;

/// 渲染帧引用类型
typedef MaskRenderFrame *MaskRenderFrameRef;

/// 创建渲染帧数据（内部分配内存）
///
/// - [frameId] 帧ID
/// - [timestamp] 时间戳
/// - [imageWidth] 图像宽度
/// - [imageHeight] 图像高度
/// - [imageDataSize] 图像数据大小
/// - [imageData] 图像数据指针
/// - [maskTextureWidth] mask纹理宽度
/// - [maskTextureHeight] mask纹理高度
/// - [maskTextureDataSize] mask纹理数据大小
/// - [maskTextureData] mask纹理数据指针
/// - [maskCount] mask数量
ATTRIBUTES MaskRenderFrameRef
maskRenderFrameCreate(
    uint32_t frameId,
    int64_t timestamp,
    uint32_t imageWidth,
    uint32_t imageHeight,
    uint32_t imageDataSize,
    const uint8_t *imageData,
    uint32_t maskTextureWidth,
    uint32_t maskTextureHeight,
    uint32_t maskTextureDataSize,
    const uint8_t *maskTextureData,
    uint32_t maskCount
);

/// 销毁渲染帧数据（释放所有分配的内存）
///
/// - [renderFrame] 渲染帧引用
ATTRIBUTES void
maskRenderFrameDestroy(MaskRenderFrameRef renderFrame);

/// 创建MaskIpc管理器
///
/// - [shmName] 共享内存名称
ATTRIBUTES MaskIpcRef
maskIpcCreate(const char *const shmName);

/// 销毁MaskIpc管理器
///
/// - [maskIpc] MaskIpc管理器引用
ATTRIBUTES void
maskIpcDestroy(MaskIpcRef maskIpc);

/// 尝试接收渲染就绪的帧数据（非阻塞，立即返回）
/// 内部会拷贝数据并解析为渲染格式
///
/// - [maskIpc] MaskIpc管理器引用
/// 返回渲染帧引用，如果没有可用数据则返回nullptr
ATTRIBUTES MaskRenderFrameRef
maskIpcTryReceiveRenderFrame(MaskIpcRef maskIpc);

/// 优化版本：尝试接收渲染帧数据并拷贝到预分配缓冲区
/// 由Dart层创建MaskRenderFrame作为buffer传递，函数内直接copy数据到buffer中
/// MaskRenderFrame内部需要预分配1080P最大分辨率的maskdata缓冲区
///
/// - [maskIpc] MaskIpc管理器引用
/// - [renderFrame] 预分配的渲染帧缓冲区（由Dart层创建和管理）
/// 返回true表示成功接收并拷贝数据，false表示无可用数据或失败
ATTRIBUTES bool
maskIpcTryReceiveRenderFrameToBuffer(MaskIpcRef maskIpc, MaskRenderFrameRef renderFrame);

/// 检查是否有可用的帧数据
///
/// - [maskIpc] MaskIpc管理器引用
ATTRIBUTES bool
maskIpcHasAvailableFrame(MaskIpcRef maskIpc);

/// 获取可用帧数量
///
/// - [maskIpc] MaskIpc管理器引用
ATTRIBUTES uint32_t
maskIpcGetAvailableFrameCount(MaskIpcRef maskIpc);

/// 检查连接状态
///
/// - [maskIpc] MaskIpc管理器引用
ATTRIBUTES bool
maskIpcIsConnected(MaskIpcRef maskIpc);

/// 获取已接收帧数
///
/// - [maskIpc] MaskIpc管理器引用
ATTRIBUTES uint64_t
maskIpcGetFramesReceived(MaskIpcRef maskIpc);

/// 获取丢弃帧数
///
/// - [maskIpc] MaskIpc管理器引用
ATTRIBUTES uint64_t
maskIpcGetFramesDropped(MaskIpcRef maskIpc);

#ifdef __cplusplus
}
#endif

#endif // MASK_IPC_FFI_H