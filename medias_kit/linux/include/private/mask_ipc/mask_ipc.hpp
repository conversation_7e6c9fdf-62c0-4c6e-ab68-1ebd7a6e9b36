#pragma once

#include <atomic>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

namespace MK {

// 帧元数据 - 确保内存对齐
struct FrameMetadata {
    int64_t timestamp;  // 时间戳（毫秒） [8字节对齐]
    uint32_t frameId;   // 帧序号
    uint32_t width;     // 图像宽度
    uint32_t height;    // 图像高度
    uint32_t imageSize; // 图像数据大小
    uint32_t maskSize;  // mask数据大小
    uint32_t reserved;  // 填充确保8字节对齐
} __attribute__((packed));

// 缓冲区状态
enum class BufferState : uint32_t {
    Empty = 0, // 空闲状态
    Writing,   // 算法进程写入中
    Ready,     // 数据就绪，待app读取
    Reading,   // app读取中
    Processed  // app处理完成，可释放
};

// Mask信息 - 已经32位对齐，无需额外填充
struct MaskInfo {
    uint32_t objectId;        // 对象ID
    uint32_t rleOffset;       // RLE数据偏移量
    uint32_t rleCountsLength; // RLE counts数组长度
    uint32_t originalWidth;   // 原始mask宽度
    uint32_t originalHeight;  // 原始mask高度
    uint32_t tissueId;        // 组织ID
} __attribute__((packed));

// Mask头部信息 - 添加填充确保对齐
struct MaskHeader {
    uint32_t maskCount;  // mask数量
    uint32_t maskWidth;  // mask宽度
    uint32_t maskHeight; // mask高度
    uint32_t reserved;   // 填充到16字节
} __attribute__((packed));

// 解码后的Mask数据
struct DecodedMask {
    uint32_t objectId;
    uint32_t width;
    uint32_t height;
    uint32_t tissueId;
    std::vector<uint8_t> maskData; // 解码后的mask数据，填充tissue_id值
};

// 帧数据包装类
class FrameData {
  public:
    FrameData(
        const FrameMetadata &metadata,
        const uint8_t *imageData,
        const uint8_t *maskData,
        size_t maskDataSize
    );

    ~FrameData();

    // 获取基本信息
    const FrameMetadata &
    getMetadata() const {
        return metadata_;
    }

    const uint8_t *
    getImageData() const {
        return imageData_.data();
    }

    size_t
    getImageSize() const {
        return metadata_.imageSize;
    }

    const std::vector<uint8_t> &
    getMaskData() const {
        return maskData_;
    }

    // 获取解码后的masks
    const std::vector<DecodedMask> &
    getDecodedMasks();

    // 获取合并后的纹理
    const std::vector<uint8_t> &
    getMergedTexture();

  private:
    FrameMetadata metadata_;
    std::vector<uint8_t> imageData_;
    std::vector<uint8_t> maskData_;
    std::vector<DecodedMask> decodedMasks_;
    std::vector<uint8_t> mergedTexture_;
    bool masksDecoded_;
    bool textureMerged_;

    void
    decodeRleMasks();

    void
    mergeMasksToTexture();
};

// MaskIpc 核心管理类
class MaskIpc {
  public:
    MaskIpc();

    ~MaskIpc();

    // 初始化和清理（APP端创建并管理共享内存）
    bool
    initialize(const std::string &shmName);

    void
    cleanup();

    // 数据接收（非阻塞式）
    bool
    hasAvailableFrame() const;

    std::unique_ptr<FrameData>
    tryReceiveFrame();

    // 状态查询
    bool
    isInitialized() const;

    uint32_t
    getAvailableFrameCount() const;

    uint64_t
    getFramesReceived() const;

    uint64_t
    getFramesDropped() const;

  public:
    struct SharedMemory;
    struct FrameBuffer;

  private:
    SharedMemory *shmHandler_;
    int shmFd_;
    std::string shmName_;
    std::atomic<bool> running_;
    mutable std::mutex statsMutex_;

    // 统计信息
    uint64_t framesReceived_;
    uint64_t framesDropped_;

    bool
    createSharedMemory();

    bool
    mapSharedMemory();

    void
    initializeSharedMemory();

    FrameBuffer *
    getReadyFrame() const;
};

} // namespace MK