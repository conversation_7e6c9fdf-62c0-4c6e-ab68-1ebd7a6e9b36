#pragma once

#include <cstddef>
#include <cstdint>
#include <vector>

namespace MK {

// RLE解码器类
class RleDecoder {
  public:
    // 将RLE counts数据解码为mask，填充tissue_id值
    // counts: RLE counts数组
    // width, height: mask尺寸
    // tissueId: 要填充的组织ID值
    // 返回: 解码后的mask数据，前景像素=tissueId，背景像素=0
    static std::vector<uint8_t>
    decodeRleToMask(
        const uint32_t *counts,
        size_t countsLength,
        uint32_t width,
        uint32_t height,
        uint32_t tissueId
    );

    // 将COCO格式RLE数据解码为mask
    // cocoRleData: COCO格式RLE数据（压缩字符串）
    // dataLength: 数据长度
    // width, height: mask尺寸
    // tissueId: 要填充的组织ID值
    // 返回: 解码后的mask数据，前景像素=tissueId，背景像素=0
    static std::vector<uint8_t>
    decodeCocoRleToMask(
        const uint8_t *cocoRleData,
        size_t dataLength,
        uint32_t width,
        uint32_t height,
        uint32_t tissueId
    );

    // 按位合并多个masks成单一纹理
    // masks: 解码后的mask数组（按优先级排序）
    // width, height: 纹理尺寸
    // 返回: 合并后的纹理，像素值为对应的tissue_id或0（背景）
    static std::vector<uint8_t>
    mergeMasksToTexture(
        const std::vector<std::vector<uint8_t>> &masks,
        uint32_t width,
        uint32_t height
    );

  private:
    // RLE解码核心实现（列优先顺序，兼容SAM2标准）
    static void
    rleDecode(
        const uint32_t *counts,
        size_t countsLength,
        uint32_t width,
        uint32_t height,
        uint8_t *maskData,
        uint8_t fillValue
    );

    // COCO RLE解码辅助函数：将COCO压缩数据解码为counts数组
    static std::vector<uint32_t>
    decodeCocoRleToCounts(
        const uint8_t *cocoRleData,
        size_t dataLength,
        uint32_t width,
        uint32_t height
    );
};

} // namespace MK