#include "renderer/texture_renderer_ffi.h"

#include "medias_kit_plugin_private.hpp"
#include "renderer/texture_renderer.hpp"
#include "utils/logger.hpp"

TextureRendererRef
textureRendererCreate(const char *const pluginId) {
    try {
        MK::TextureRenderer *renderer = nullptr;
        MK::findPlugin(pluginId, [&](const std::string pluginId, const MediasKitPlugin &plugin) {
            renderer = new MK::TextureRenderer(plugin);
            if (!renderer->init()) {
                delete renderer;
                renderer = nullptr;
            }
        });
        return renderer;
    } catch (const std::exception &e) {
        MK::LOG_E("Failed to create texture renderer: {}", e.what());
        return nullptr;
    }
}

void
textureRendererDestroy(const TextureRendererRef renderer) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    delete rendererPtr;
}

void
textureRendererRenderRgbaFrame(
    const TextureRendererRef renderer,
    const uint8_t *const frame,
    const int64_t frameLen,
    const int64_t width,
    const int64_t height
) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    rendererPtr->renderRgbaFrame(frame, frameLen, width, height);
}

void
textureRendererRenderRgbaFrameWithMask(
    const TextureRendererRef renderer,
    const uint8_t *const frame,
    const int64_t frameLen,
    const int64_t width,
    const int64_t height,
    const uint8_t *const maskData,
    const int64_t maskWidth,
    const int64_t maskHeight,
    const uint32_t *const colors,
    const int64_t colorCount
) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    std::vector<uint32_t> colorArray(colors, colors + colorCount);
    rendererPtr->renderRgbaFrame(frame, frameLen, width, height, maskData, maskWidth, maskHeight, colorArray);
}

int64_t
textureRendererGetTextureId(const TextureRendererRef renderer) {
    assert(renderer);
    auto *rendererPtr = static_cast<MK::TextureRenderer *>(renderer);
    return rendererPtr->getTexture();
}
