#include "renderer/texture_renderer.hpp"

#include <cstdint>
#include <cstring>
#include <epoxy/gl.h>
#include <iostream>
#include <mpv/render.h>
#include <mpv/render_gl.h>
#include <ostream>
#include <thread>

#include "utils/logger.hpp"

namespace MK {

// 结构体定义移到顶部
struct _MKTextureGL {
    FlTextureGL parent_instance;
    GLuint textureId;
    GLuint framebufferId;
    guint32 width;
    guint32 height;
    TextureRenderer *renderer;
    mpv_render_context *renderContext;

    // Mask 相关 OpenGL 资源
    GLuint maskTextureId;
    GLuint maskShaderProgram;
    GLint tissueColorsUniform;
    GLint videoTextureUniform;
    GLint maskTextureUniform;

    // 过渡纹理用于mask渲染双缓冲
    GLuint transitionTextureId;
    GLuint transitionFramebufferId;
};

G_DEFINE_TYPE(
    MKTextureGL,
    texture_gl,
    fl_texture_gl_get_type()
)

// 片段着色器代码，支持色相混合以保留亮度细节, tissueColors 最多支持20种颜色
const char *maskFragmentShaderSource = R"(
#version 330 core
in vec2 TexCoord;
out vec4 FragColor;

uniform sampler2D videoTexture;
uniform sampler2D maskTexture;
uniform uint tissueColors[20];

// RGB转HSV函数
vec3 rgb2hsv(vec3 c) {
    vec4 K = vec4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
    vec4 p = mix(vec4(c.bg, K.wz), vec4(c.gb, K.xy), step(c.b, c.g));
    vec4 q = mix(vec4(p.xyw, c.r), vec4(c.r, p.yzx), step(p.x, c.r));
    
    float d = q.x - min(q.w, q.y);
    float e = 1.0e-10;
    return vec3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

// HSV转RGB函数
vec3 hsv2rgb(vec3 c) {
    vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
    vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);
    return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);
}

void main() {
    // 采样视频纹理
    vec4 videoColor = texture(videoTexture, TexCoord);
    
    // 采样 mask 纹理获取 tissueId
    float tissueId = texture(maskTexture, TexCoord).r * 255.0;
    int tissueIndex = int(tissueId);
    
    if (tissueIndex == 0 || tissueIndex > 20) {
        // 背景或超出范围，只显示 video
        FragColor = videoColor;
    } else {
        // 获取组织颜色 (RGBA packed in uint) - mask索引1对应数组索引0
        uint colorValue = tissueColors[tissueIndex - 1];
        vec3 maskRGB = vec3(
            float((colorValue >> 24) & 0xFFu) / 255.0,
            float((colorValue >> 16) & 0xFFu) / 255.0,
            float((colorValue >> 8) & 0xFFu) / 255.0
        );
        float maskAlpha = float(colorValue & 0xFFu) / 255.0;
        
        // 纯色相混合：保留细节的色调调整
        vec3 videoHSV = rgb2hsv(videoColor.rgb);
        vec3 maskHSV = rgb2hsv(maskRGB);
        vec3 hueBlendedHSV = vec3(
            maskHSV.x,      // 使用mask的色相
            videoHSV.y,     // 保留视频的饱和度
            videoHSV.z      // 保留视频的亮度
        );
        vec3 hueBlend = hsv2rgb(hueBlendedHSV);
        
        // 直接使用色相混合，不进行色块覆盖
        vec3 finalBlend = mix(videoColor.rgb, hueBlend, maskAlpha);
        
        FragColor = vec4(finalBlend, videoColor.a);
    }
}
)";

// 顶点着色器代码
const char *maskVertexShaderSource = R"(
#version 330 core
layout (location = 0) in vec2 position;
layout (location = 1) in vec2 texCoord;

out vec2 TexCoord;

void main() {
    gl_Position = vec4(position, 0.0, 1.0);
    TexCoord = texCoord;
}
)";

GLuint
compileShader(GLenum type, const char *source) {
    GLuint shader = glCreateShader(type);
    glShaderSource(shader, 1, &source, nullptr);
    glCompileShader(shader);

    GLint success;
    glGetShaderiv(shader, GL_COMPILE_STATUS, &success);
    if (!success) {
        char infoLog[512];
        glGetShaderInfoLog(shader, 512, nullptr, infoLog);
        LOG_E("Shader compilation failed: {}", infoLog);
        glDeleteShader(shader);
        return 0;
    }

    return shader;
}

GLuint
createMaskShaderProgram() {
    GLuint vertexShader = compileShader(GL_VERTEX_SHADER, maskVertexShaderSource);
    if (vertexShader == 0) return 0;

    GLuint fragmentShader = compileShader(GL_FRAGMENT_SHADER, maskFragmentShaderSource);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        return 0;
    }

    GLuint program = glCreateProgram();
    glAttachShader(program, vertexShader);
    glAttachShader(program, fragmentShader);
    glLinkProgram(program);

    GLint success;
    glGetProgramiv(program, GL_LINK_STATUS, &success);
    if (!success) {
        char infoLog[512];
        glGetProgramInfoLog(program, 512, nullptr, infoLog);
        LOG_E("Shader program linking failed: {}", infoLog);
        glDeleteProgram(program);
        program = 0;
    }

    glDeleteShader(vertexShader);
    glDeleteShader(fragmentShader);

    return program;
}

GLuint
generateEmptyTexture2D(
    const GLenum innerFormat,
    const GLenum pixelFormat,
    const GLsizei width = 1920,
    const GLsizei height = 1080
) {
    GLuint texture;
    glGenTextures(1, &texture);
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexImage2D(GL_TEXTURE_2D, 0, innerFormat, width, height, 0, pixelFormat, GL_UNSIGNED_BYTE, nullptr);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_S, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_WRAP_T, GL_CLAMP_TO_EDGE);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MAG_FILTER, GL_NEAREST);
    glTexParameteri(GL_TEXTURE_2D, GL_TEXTURE_MIN_FILTER, GL_NEAREST);
    glBindTexture(GL_TEXTURE_2D, 0);
    return texture;
}

GLuint
generateFrameBufferTexture2D(const GLuint texture) {
    GLuint framebuffer;
    glGenFramebuffers(1, &framebuffer);
    glBindFramebuffer(GL_FRAMEBUFFER, framebuffer);
    glFramebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, texture, 0);
    GLenum status = glCheckFramebufferStatus(GL_FRAMEBUFFER);
    if (status != GL_FRAMEBUFFER_COMPLETE) {
        LOG_E("Framebuffer incomplete: 0x{:x}", status);
    }
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    return framebuffer;
}

void
commitTexture2DPixels(
    const GLuint texture,
    const GLenum innerFormat,
    const GLenum pixelFormat,
    const GLsizei width,
    const GLsizei height,
    const GLenum type,
    const GLvoid *const pixels
) {
    glBindTexture(GL_TEXTURE_2D, texture);
    glTexImage2D(
        GL_TEXTURE_2D,
        0,
        innerFormat,
        width,
        height,
        0,
        pixelFormat,
        type,
        pixels
    );
    // glGenerateMipmap(GL_TEXTURE_2D);
    glBindTexture(GL_TEXTURE_2D, 0);
}

void
renderFullscreenQuad() {
    // 全屏四边形的顶点数据 (位置 + 纹理坐标)
    static const float quadVertices[] = {
        // 位置      // 纹理坐标
        -1.0f,
        1.0f,
        0.0f,
        1.0f,
        -1.0f,
        -1.0f,
        0.0f,
        0.0f,
        1.0f,
        -1.0f,
        1.0f,
        0.0f,
        -1.0f,
        1.0f,
        0.0f,
        1.0f,
        1.0f,
        -1.0f,
        1.0f,
        0.0f,
        1.0f,
        1.0f,
        1.0f,
        1.0f};

    static GLuint VAO = 0, VBO = 0;

    // 初始化 VAO/VBO (只执行一次)
    if (VAO == 0) {
        glGenVertexArrays(1, &VAO);
        glGenBuffers(1, &VBO);

        glBindVertexArray(VAO);
        glBindBuffer(GL_ARRAY_BUFFER, VBO);
        glBufferData(GL_ARRAY_BUFFER, sizeof(quadVertices), quadVertices, GL_STATIC_DRAW);

        // 位置属性
        glEnableVertexAttribArray(0);
        glVertexAttribPointer(0, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void *)0);

        // 纹理坐标属性
        glEnableVertexAttribArray(1);
        glVertexAttribPointer(1, 2, GL_FLOAT, GL_FALSE, 4 * sizeof(float), (void *)(2 * sizeof(float)));

        glBindVertexArray(0);
    }

    // 渲染四边形
    glBindVertexArray(VAO);
    glDrawArrays(GL_TRIANGLES, 0, 6);
    glBindVertexArray(0);
}

void
renderWithMask(MKTextureGL *self) {
    // 创建 mask 相关纹理和着色器
    if (self->maskTextureId == 0) {
        // *** 创建 texture、framebuffer 等一定要在 texture_gl_populate_texture 中完成, 否则会出现不可预知的错误
        self->maskTextureId = generateEmptyTexture2D(GL_RED, GL_RED);
        self->transitionTextureId = generateEmptyTexture2D(GL_RGBA, GL_RGBA, self->width, self->height);
        self->transitionFramebufferId = generateFrameBufferTexture2D(self->transitionTextureId);

        if (self->maskShaderProgram == 0) {
            self->maskShaderProgram = createMaskShaderProgram();
            if (self->maskShaderProgram > 0) {
                self->tissueColorsUniform = glGetUniformLocation(self->maskShaderProgram, "tissueColors");
                self->videoTextureUniform = glGetUniformLocation(self->maskShaderProgram, "videoTexture");
                self->maskTextureUniform = glGetUniformLocation(self->maskShaderProgram, "maskTexture");
            }
        }
    }

    // 采集卡数据直接提交到过渡纹理
    commitTexture2DPixels(
        self->transitionTextureId,
        GL_RGBA,
        GL_RGBA,
        self->width,
        self->height,
        GL_UNSIGNED_BYTE,
        self->renderer->imageBuffer
    );

    // 上传 mask 纹理数据
    commitTexture2DPixels(
        self->maskTextureId,
        GL_RED,
        GL_RED,
        self->renderer->maskWidth,
        self->renderer->maskHeight,
        GL_UNSIGNED_BYTE,
        self->renderer->maskBuffer
    );

    glBindFramebuffer(GL_FRAMEBUFFER, self->framebufferId);

    glViewport(0, 0, self->width, self->height);

    // 使用 mask 着色器程序
    glUseProgram(self->maskShaderProgram);

    // 绑定过渡纹理作为视频输入（纹理单元 0）
    glActiveTexture(GL_TEXTURE0);
    glBindTexture(GL_TEXTURE_2D, self->transitionTextureId);
    glUniform1i(self->videoTextureUniform, 0);

    // 绑定 mask 纹理到纹理单元 1
    glActiveTexture(GL_TEXTURE1);
    glBindTexture(GL_TEXTURE_2D, self->maskTextureId);
    glUniform1i(self->maskTextureUniform, 1);

    // 上传颜色数组
    if (!self->renderer->tissueColors.empty() && self->tissueColorsUniform >= 0) {
        size_t colorCount = std::min(self->renderer->tissueColors.size(), size_t(20));
        glUniform1uiv(self->tissueColorsUniform, colorCount, self->renderer->tissueColors.data());
    }

    renderFullscreenQuad();

    // 恢复状态
    glBindFramebuffer(GL_FRAMEBUFFER, 0);
    glUseProgram(0);
    glActiveTexture(GL_TEXTURE0);
}

gboolean
texture_gl_populate_texture(
    FlTextureGL *texture,
    uint32_t *target,
    uint32_t *name,
    uint32_t *width,
    uint32_t *height,
    GError **error
) {
    MKTextureGL *self = TEXTURE_GL(texture);
    if (self->textureId == 0) {
        // *** 创建 texture、framebuffer 等一定要在 texture_gl_populate_texture 中完成, 否则会出现不可预知的错误
        self->textureId = generateEmptyTexture2D(GL_RGBA, GL_RGBA);
        self->framebufferId = generateFrameBufferTexture2D(self->textureId);
    }

    *target = GL_TEXTURE_2D;
    *name = self->textureId;
    *width = self->width;
    *height = self->height;
    if (self->width > 0 && self->height > 0) {
        // 会导致 Flutter 页面中使用到 openGL 相关的API的组件闪烁
        // glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
        std::lock_guard<std::mutex> lock(self->renderer->mtx);
        if (self->renderContext) { // 当前未实现 renderContext 并未初始化过
            mpv_opengl_fbo fbo{(int)self->framebufferId, (int)self->width, (int)self->height, 0};
            mpv_render_param params[] = {
                {MPV_RENDER_PARAM_OPENGL_FBO, &fbo},
                {   MPV_RENDER_PARAM_INVALID, NULL},
            };
            glBindTexture(GL_TEXTURE_2D, self->textureId);
            glBindFramebuffer(GL_FRAMEBUFFER, self->framebufferId);
            GLenum status = glCheckFramebufferStatus(GL_FRAMEBUFFER);
            if (status != GL_FRAMEBUFFER_COMPLETE) {
                LOG_E("FBO is incomplete: 0x{:x}", status);
            }
            // 帧对象直接拷贝
            auto ret = mpv_render_context_render(self->renderContext, params);
            if (ret < 0) {
                LOG_E("mpv_render_context_render failure! ret = {}", mpv_error_string(ret));
            }
            glBindFramebuffer(GL_FRAMEBUFFER, 0);
            glBindTexture(GL_TEXTURE_2D, 0);
            self->renderContext = nullptr;
        } else {
            // 如果启用了 mask，数据直接提交到过渡纹理
            if (self->renderer->maskEnabled && self->renderer->maskBuffer) {
                // 恢复mask处理进行调试
                renderWithMask(self);
            } else {
                // 无mask时，直接提交到textureId
                commitTexture2DPixels(
                    self->textureId,
                    GL_RGBA,
                    GL_RGBA,
                    self->width,
                    self->height,
                    GL_UNSIGNED_BYTE,
                    self->renderer->imageBuffer
                );
            }
        }

        return TRUE;
    }
    g_set_error_literal(
        error,
        G_FILE_ERROR,
        G_FILE_ERROR_FAILED,
        "Invalid parameters!"
    );
    return FALSE;
}

void
texture_gl_dispose(GObject *object) {
    MKTextureGL *self = TEXTURE_GL(object);
    // Free texture & FBO.
    if (self->framebufferId != 0) {
        glDeleteFramebuffers(1, &self->framebufferId);
        self->framebufferId = 0;
    }
    if (self->textureId != 0) {
        glDeleteTextures(1, &self->textureId);
        self->textureId = 0;
    }

    // 释放 mask 相关资源
    if (self->maskTextureId != 0) {
        glDeleteTextures(1, &self->maskTextureId);
        self->maskTextureId = 0;
    }
    if (self->maskShaderProgram != 0) {
        glDeleteProgram(self->maskShaderProgram);
        self->maskShaderProgram = 0;
    }

    // 释放过渡纹理资源
    if (self->transitionTextureId != 0) {
        glDeleteTextures(1, &self->transitionTextureId);
        self->transitionTextureId = 0;
    }
    if (self->transitionFramebufferId != 0) {
        glDeleteFramebuffers(1, &self->transitionFramebufferId);
        self->transitionFramebufferId = 0;
    }

    self->width = 0;
    self->height = 0;
    self->renderer = nullptr;
    self->renderContext = nullptr;
    G_OBJECT_CLASS(texture_gl_parent_class)->dispose(object);
    LOG_I("{}", "Memory free!");
}

void
texture_gl_class_init(MKTextureGLClass *klass) {
    FL_TEXTURE_GL_CLASS(klass)->populate = texture_gl_populate_texture;
    G_OBJECT_CLASS(klass)->dispose = texture_gl_dispose;
}

void
texture_gl_init(MKTextureGL *self) {
    self->textureId = 0;
    self->framebufferId = 0;
    self->width = 0;
    self->height = 0;
    self->renderer = nullptr;
    self->renderContext = nullptr;

    // 初始化 Mask 相关
    self->maskTextureId = 0;
    self->maskShaderProgram = 0;
    self->tissueColorsUniform = -1;
    self->videoTextureUniform = -1;
    self->maskTextureUniform = -1;

    // 初始化过渡纹理
    self->transitionTextureId = 0;
    self->transitionFramebufferId = 0;
}

MKTextureGL *
texture_gl_new(TextureRenderer *renderer) {
    MKTextureGL *self = TEXTURE_GL(
        g_object_new(
            texture_gl_get_type(),
            NULL
        )
    );
    self->renderer = renderer;

    return self;
}

TextureRenderer::TextureRenderer(const MediasKitPlugin &plugin)
    : plugin(plugin),
      imageBuffer(new std::uint8_t[3840 * 2160 * 4]()) {}

TextureRenderer::~TextureRenderer() {
    std::lock_guard<std::mutex> lock(mtx);
    fl_texture_registrar_unregister_texture(
        plugin.textureRegistrar,
        FL_TEXTURE(texture)
    );
    textureId = 0;
    if (texture) {
        g_object_unref(texture);
    }
    if (glContext) {
        g_object_unref(glContext);
    }
    delete[] imageBuffer;
    LOG_I("{}", "Memory free!");
}

bool
TextureRenderer::init() {
    auto textureRegistrar = plugin.textureRegistrar;
    auto view = plugin.view;
    auto window = gtk_widget_get_window(GTK_WIDGET(view));
    GError *error = nullptr;
    // WARNING: 不需要手动释放
    glContext = gdk_window_create_gl_context(window, &error);
    if (error) {
        LOG_E("gdk_window_create_gl_context, {}", error->message);
        g_error_free(error);
        return false;
    }
    gdk_gl_context_make_current(glContext);
    glClearColor(0.0, 0.0, 0.0, 1.0);
    gdk_gl_context_realize(glContext, &error);
    if (error) {
        LOG_E("gdk_gl_context_realize, {}", error->message);
        g_error_free(error);
        return false;
    }
    this->texture = texture_gl_new(this);
    if (!this->texture) {
        LOG_E("{}", "texture_gl_new failure!");
        return false;
    }
    if (!fl_texture_registrar_register_texture(
            textureRegistrar,
            FL_TEXTURE(texture)
        )) {
        LOG_E("{}", "fl_texture_registrar_register_texture failure!");
        return false;
    }
    return true;
}

void
TextureRenderer::renderRgbaFrame(
    const uint8_t *const frame,
    const int64_t frameLen,
    const int64_t width,
    const int64_t height
) {
    std::lock_guard<std::mutex> lock(mtx);
    texture->width = width;
    texture->height = height;
    maskEnabled = false; // 禁用 mask
    memcpy(imageBuffer, frame, frameLen);
    fl_texture_registrar_mark_texture_frame_available(
        plugin.textureRegistrar,
        FL_TEXTURE(texture)
    );
}

void
TextureRenderer::renderRgbaFrame(
    const uint8_t *const frame,
    const int64_t frameLen,
    const int64_t width,
    const int64_t height,
    const uint8_t *maskData,
    const uint32_t maskWidth,
    const uint32_t maskHeight,
    const std::vector<uint32_t> &colors
) {
    std::lock_guard<std::mutex> lock(mtx);
    this->texture->width = width;
    this->texture->height = height;

    // 设置 mask 相关参数
    this->maskBuffer = maskData;
    this->maskWidth = maskWidth;
    this->maskHeight = maskHeight;
    this->maskEnabled = maskData && maskWidth > 0 && maskHeight > 0;
    this->tissueColors = colors;

    memcpy(imageBuffer, frame, frameLen);
    fl_texture_registrar_mark_texture_frame_available(
        plugin.textureRegistrar,
        FL_TEXTURE(texture)
    );
}

int64_t
TextureRenderer::getTexture() const {
    return (int64_t)texture;
}

} // namespace MK
