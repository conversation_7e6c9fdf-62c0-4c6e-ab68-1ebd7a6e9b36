#include "mask_ipc/mask_ipc_ffi.h"
#include "mask_ipc/mask_ipc.hpp"
#include "utils/logger.hpp"

#include <cassert>
#include <memory>

using namespace MK;

// MaskRenderFrame内存管理函数实现
MaskRenderFrameRef
maskRenderFrameCreate(
    uint32_t frameId,
    int64_t timestamp,
    uint32_t imageWidth,
    uint32_t imageHeight,
    uint32_t imageDataSize,
    const uint8_t *imageData,
    uint32_t maskTextureWidth,
    uint32_t maskTextureHeight,
    uint32_t maskTextureDataSize,
    const uint8_t *maskTextureData,
    uint32_t maskCount
) {
    // 分配主结构体
    auto *renderFrame = new MaskRenderFrame;
    if (!renderFrame) {
        LOG_E("{}", "Failed to allocate MaskRenderFrame");
        return nullptr;
    }

    // 设置基本信息
    renderFrame->frameId = frameId;
    renderFrame->timestamp = timestamp;
    renderFrame->imageWidth = imageWidth;
    renderFrame->imageHeight = imageHeight;
    renderFrame->imageDataSize = imageDataSize;
    renderFrame->maskTextureWidth = maskTextureWidth;
    renderFrame->maskTextureHeight = maskTextureHeight;
    renderFrame->maskTextureDataSize = maskTextureDataSize;
    renderFrame->maskCount = maskCount;

    // 分配并拷贝图像数据
    renderFrame->imageData = new uint8_t[imageDataSize];
    if (imageData) {
        std::memcpy(renderFrame->imageData, imageData, imageDataSize);
    } else {
        renderFrame->imageDataSize = 0;
    }

    // 分配并拷贝mask纹理数据
    renderFrame->maskTextureData = new uint8_t[maskTextureDataSize];
    if (maskTextureData) {
        std::memcpy(renderFrame->maskTextureData, maskTextureData, maskTextureDataSize);
    } else {
        renderFrame->maskTextureDataSize = 0;
    }
    return renderFrame;
}

void
maskRenderFrameDestroy(MaskRenderFrameRef renderFrame) {
    if (!renderFrame) {
        return;
    }

    // 释放图像数据
    if (renderFrame->imageData) {
        delete[] renderFrame->imageData;
    }

    // 释放mask纹理数据
    if (renderFrame->maskTextureData) {
        delete[] renderFrame->maskTextureData;
    }

    // 释放主结构体
    delete renderFrame;
}

MaskIpcRef
maskIpcCreate(const char *const shmName) {
    if (!shmName) {
        LOG_E("{}", "Shared memory name is null");
        return nullptr;
    }

    auto maskIpc = std::make_unique<MaskIpc>();
    if (!maskIpc->initialize(std::string(shmName))) {
        LOG_E("Failed to initialize MaskIpc with name: {}", shmName);
        return nullptr;
    }

    LOG_I("MaskIpc created successfully with name: {}", shmName);
    return maskIpc.release();
}

void
maskIpcDestroy(MaskIpcRef maskIpc) {
    if (!maskIpc) {
        return;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);
    delete maskIpcPtr;
    LOG_I("{}", "MaskIpc destroyed");
}

MaskRenderFrameRef
maskIpcTryReceiveRenderFrame(MaskIpcRef maskIpc) {
    if (!maskIpc) {
        LOG_E("{}", "MaskIpc reference is null");
        return nullptr;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);

    // 非阻塞尝试接收
    auto frameData = maskIpcPtr->tryReceiveFrame();
    if (!frameData) {
        return nullptr;
    }

    // 获取解码后的masks和合并纹理
    const auto &decodedMasks = frameData->getDecodedMasks();
    const auto &mergedTexture = frameData->getMergedTexture();
    const auto &metadata = frameData->getMetadata();

    // 获取mask的真实尺寸
    uint32_t maskWidth = 0;
    uint32_t maskHeight = 0;

    // 如果有mask数据，从header获取真实尺寸
    if (!frameData->getMaskData().empty()) {
        const MaskHeader *header = reinterpret_cast<const MaskHeader *>(frameData->getMaskData().data());
        maskWidth = header->maskWidth;
        maskHeight = header->maskHeight;
    }

    // 创建渲染帧（拷贝所有数据）
    return maskRenderFrameCreate(
        metadata.frameId,
        metadata.timestamp,
        metadata.width,
        metadata.height,
        frameData->getImageSize(),
        frameData->getImageData(),
        maskWidth, // 使用真实mask尺寸
        maskHeight,
        static_cast<uint32_t>(mergedTexture.size()),
        mergedTexture.data(),
        static_cast<uint32_t>(decodedMasks.size())
    );
}

bool
maskIpcTryReceiveRenderFrameToBuffer(MaskIpcRef maskIpc, MaskRenderFrameRef renderFrame) {
    if (!maskIpc || !renderFrame) {
        LOG_E("{}", "MaskIpc reference or renderFrame is null");
        return false;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);

    // 非阻塞尝试接收
    auto frameData = maskIpcPtr->tryReceiveFrame();
    if (!frameData) {
        return false;
    }

    // 获取解码后的masks和合并纹理
    const auto &decodedMasks = frameData->getDecodedMasks();
    const auto &mergedTexture = frameData->getMergedTexture();
    const auto &metadata = frameData->getMetadata();

    // 获取mask的真实尺寸
    uint32_t maskWidth = 0;
    uint32_t maskHeight = 0;

    // 如果有mask数据，从header获取真实尺寸
    if (!frameData->getMaskData().empty()) {
        const MaskHeader *header = reinterpret_cast<const MaskHeader *>(frameData->getMaskData().data());
        maskWidth = header->maskWidth;
        maskHeight = header->maskHeight;
    }

    // 验证缓冲区大小是否足够（1080P限制）
    const uint32_t imageSize = frameData->getImageSize();
    const uint32_t maskSize = static_cast<uint32_t>(mergedTexture.size());

    // 1080P RGBA最大大小检查
    const uint32_t maxImageSize = 1920 * 1080 * 4; // RGBA
    const uint32_t maxMaskSize = 1920 * 1080;      // 单通道

    if (imageSize > maxImageSize || maskSize > maxMaskSize) {
        LOG_E("Image size {} or mask size {} exceeds 1080P limits ({}, {})", imageSize, maskSize, maxImageSize, maxMaskSize);
        return false;
    }

    // 直接拷贝数据到预分配的缓冲区
    // 设置基本信息
    renderFrame->frameId = metadata.frameId;
    renderFrame->timestamp = metadata.timestamp;
    renderFrame->imageWidth = metadata.width;
    renderFrame->imageHeight = metadata.height;
    renderFrame->imageDataSize = imageSize;
    renderFrame->maskTextureWidth = maskWidth;
    renderFrame->maskTextureHeight = maskHeight;
    renderFrame->maskTextureDataSize = maskSize;
    renderFrame->maskCount = static_cast<uint32_t>(decodedMasks.size());

    // 拷贝图像数据（假设imageData指向缓冲区内的数据）
    if (renderFrame->imageData) {
        std::memcpy(renderFrame->imageData, frameData->getImageData(), imageSize);
    }

    // 拷贝mask纹理数据
    if (renderFrame->maskTextureData) {
        std::memcpy(renderFrame->maskTextureData, mergedTexture.data(), maskSize);
    }

    return true;
}

bool
maskIpcHasAvailableFrame(MaskIpcRef maskIpc) {
    if (!maskIpc) {
        return false;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);
    return maskIpcPtr->hasAvailableFrame();
}

uint32_t
maskIpcGetAvailableFrameCount(MaskIpcRef maskIpc) {
    if (!maskIpc) {
        return 0;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);
    return maskIpcPtr->getAvailableFrameCount();
}

bool
maskIpcIsConnected(MaskIpcRef maskIpc) {
    if (!maskIpc) {
        return false;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);
    return maskIpcPtr->isInitialized();
}

uint64_t
maskIpcGetFramesReceived(MaskIpcRef maskIpc) {
    if (!maskIpc) {
        return 0;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);
    return maskIpcPtr->getFramesReceived();
}

uint64_t
maskIpcGetFramesDropped(MaskIpcRef maskIpc) {
    if (!maskIpc) {
        return 0;
    }

    auto maskIpcPtr = static_cast<MaskIpc *>(maskIpc);
    return maskIpcPtr->getFramesDropped();
}
