#include "mask_ipc/rle_decoder.hpp"
#include "utils/logger.hpp"

#include <cstring>

namespace MK {

std::vector<uint8_t>
RleDecoder::decodeRleToMask(
    const uint32_t *counts,
    size_t countsLength,
    uint32_t width,
    uint32_t height,
    uint32_t tissueId
) {

    std::vector<uint8_t> maskData(width * height, 0);

    if (!counts || countsLength == 0 || width == 0 || height == 0) {
        return maskData;
    }

    // 确保tissueId在合理范围内（1-255），0保留给背景
    uint8_t fillValue = (tissueId > 0 && tissueId <= 255) ? static_cast<uint8_t>(tissueId) : 1;

    rleDecode(counts, countsLength, width, height, maskData.data(), fillValue);

    return maskData;
}

std::vector<uint8_t>
RleDecoder::decodeCocoRleToMask(
    const uint8_t *cocoRleData,
    size_t dataLength,
    uint32_t width,
    uint32_t height,
    uint32_t tissueId
) {

    std::vector<uint8_t> maskData(width * height, 0);

    if (!cocoRleData || dataLength == 0 || width == 0 || height == 0) {
        return maskData;
    }

    // 第一步：将COCO RLE压缩数据解码为counts数组
    std::vector<uint32_t> counts = decodeCocoRleToCounts(cocoRleData, dataLength, width, height);
    
    if (counts.empty()) {
        return maskData;
    }

    // 第二步：使用标准RLE解码逻辑
    uint8_t fillValue = (tissueId > 0 && tissueId <= 255) ? static_cast<uint8_t>(tissueId) : 1;
    rleDecode(counts.data(), counts.size(), width, height, maskData.data(), fillValue);

    return maskData;
}

std::vector<uint8_t>
RleDecoder::mergeMasksToTexture(
    const std::vector<std::vector<uint8_t>> &masks,
    uint32_t width,
    uint32_t height
) {

    std::vector<uint8_t> mergedTexture(width * height, 0);

    if (masks.empty()) {
        return mergedTexture;
    }

    const size_t totalPixels = width * height;

    // 按顺序合并masks，后面的覆盖前面的（优先级：数组索引越大优先级越高）
    for (size_t maskIndex = 0; maskIndex < masks.size(); ++maskIndex) {
        const auto &mask = masks[maskIndex];

        if (mask.size() != totalPixels) {
            LOG_W("Mask {} size mismatch: expected {}, got {}", maskIndex, totalPixels, mask.size());
            continue;
        }

        // 逐像素合并：非零像素覆盖之前的值
        for (size_t pixelIndex = 0; pixelIndex < totalPixels; ++pixelIndex) {
            if (mask[pixelIndex] != 0) {
                // 前景像素：使用当前mask的tissue_id值覆盖
                mergedTexture[pixelIndex] = mask[pixelIndex];
            }
            // 背景像素(0)：保持之前的值不变
        }
    }
    return mergedTexture;
}

void
RleDecoder::rleDecode(
    const uint32_t *counts,
    size_t countsLength,
    uint32_t width,
    uint32_t height,
    uint8_t *maskData,
    uint8_t fillValue
) {

    // 初始化为背景（0）
    std::memset(maskData, 0, width * height);

    uint32_t pixelIndex = 0;
    bool isForeground = false; // RLE从背景开始
    const uint32_t totalPixels = width * height;

    // 按列优先顺序解码（Fortran order，兼容SAM2标准）
    for (size_t i = 0; i < countsLength && pixelIndex < totalPixels; ++i) {
        uint32_t runLength = counts[i];

        for (uint32_t j = 0; j < runLength && pixelIndex < totalPixels; ++j) {
            // 将列优先索引转换为行优先索引
            uint32_t col = pixelIndex / height;
            uint32_t row = pixelIndex % height;
            uint32_t actualIndex = row * width + col;

            if (actualIndex < totalPixels) {
                if (isForeground) {
                    // 前景像素：填充tissue_id值
                    maskData[actualIndex] = fillValue;
                } else {
                    // 背景像素：保持为0
                    maskData[actualIndex] = 0;
                }
            }

            pixelIndex++;
        }

        // 切换前景/背景状态
        isForeground = !isForeground;
    }
}

// COCO RLE辅助结构（来自maskApi.h）
struct CocoRLE {
    uint32_t h, w, m;  // height, width, counts数量
    uint32_t *cnts;    // counts数组
};

// COCO字符串解码函数（改编自maskApi.c的rleFrString）
static void cocoRleFrString(CocoRLE *R, const char *s, uint32_t h, uint32_t w) {
    uint32_t m = 0, p = 0, k;
    int64_t x;
    int more;
    
    // 计算字符串长度
    while (s[m]) m++;
    
    uint32_t *cnts = new uint32_t[m];
    m = 0;
    
    while (s[p]) {
        x = 0;
        k = 0;
        more = 1;
        
        while (more) {
            char c = s[p] - 48;
            x |= (c & 0x1f) << (5 * k);
            more = c & 0x20;
            p++;
            k++;
            if (!more && (c & 0x10)) {
                x |= -1 << (5 * k);
            }
        }
        
        if (m > 2) {
            x += (int64_t)cnts[m - 2];
        }
        cnts[m++] = (uint32_t)x;
    }
    
    // 初始化CocoRLE结构
    R->h = h;
    R->w = w;
    R->m = m;
    R->cnts = new uint32_t[m];
    for (uint32_t i = 0; i < m; i++) {
        R->cnts[i] = cnts[i];
    }
    
    delete[] cnts;
}

// 释放CocoRLE结构
static void cocoRleFree(CocoRLE *R) {
    delete[] R->cnts;
    R->cnts = nullptr;
}

std::vector<uint32_t>
RleDecoder::decodeCocoRleToCounts(
    const uint8_t *cocoRleData,
    size_t dataLength,
    uint32_t width,
    uint32_t height
) {
    std::vector<uint32_t> counts;
    
    if (!cocoRleData || dataLength == 0) {
        return counts;
    }
    
    // 将字节数据转换为字符串（COCO RLE是字符串格式）
    std::string cocoString(reinterpret_cast<const char*>(cocoRleData), dataLength);
    
    // 使用COCO解码函数
    CocoRLE rle;
    cocoRleFrString(&rle, cocoString.c_str(), height, width);
    
    // 转换为vector
    if (rle.m > 0 && rle.cnts) {
        counts.resize(rle.m);
        for (uint32_t i = 0; i < rle.m; i++) {
            counts[i] = rle.cnts[i];
        }
    }
    
    // 清理资源
    cocoRleFree(&rle);
    
    return counts;
}

} // namespace MK