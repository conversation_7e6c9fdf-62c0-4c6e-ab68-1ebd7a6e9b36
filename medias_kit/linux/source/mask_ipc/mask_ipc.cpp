#include "mask_ipc/mask_ipc.hpp"
#include "mask_ipc/rle_decoder.hpp"
#include "utils/logger.hpp"

#include <chrono>
#include <cstring>
#include <errno.h>
#include <fcntl.h>
#include <pthread.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <time.h>
#include <unistd.h>

namespace MK {

// 共享内存布局定义 - 简化版，无复杂同步
struct MaskIpc::FrameBuffer {
    FrameMetadata metadata;
    BufferState state;
    uint32_t maskDataSize;
    uint32_t reserved;          // 内存对齐填充
    uint8_t imageData[8294400]; // 1920x1080x4 RGBA
    uint8_t maskData[2097152];  // 2MB固定mask区域
} __attribute__((packed));

struct MaskIpc::SharedMemory {
    uint32_t magicNumber; // 魔数校验: 0x4D4B5348('MKSH')
    uint32_t version;     // 版本号
    uint32_t bufferCount; // 缓冲区数量(4)
    uint32_t writeIndex;  // 写入索引
    uint32_t readIndex;   // 读取索引
    uint32_t reserved;    // 内存对齐填充

    // 统计信息
    uint64_t framesProduced;
    uint64_t framesConsumed;
    uint64_t framesDropped;

    FrameBuffer buffers[4];
} __attribute__((packed));

static constexpr uint32_t MAGIC_NUMBER = 0x4D4B5348; // 'MKSH'
static constexpr uint32_t VERSION = 1;
static constexpr size_t TOTAL_SHM_SIZE = sizeof(MaskIpc::SharedMemory);

// FrameData实现
FrameData::FrameData(
    const FrameMetadata &metadata,
    const uint8_t *imageData,
    const uint8_t *maskData,
    size_t maskDataSize
)
    : metadata_(metadata), masksDecoded_(false), textureMerged_(false) {

    // 复制图像数据
    imageData_.resize(metadata_.imageSize);
    if (imageData && metadata_.imageSize > 0) {
        std::memcpy(imageData_.data(), imageData, metadata_.imageSize);
    }

    // 复制mask数据
    if (maskData && maskDataSize > 0) {
        maskData_.resize(maskDataSize);
        std::memcpy(maskData_.data(), maskData, maskDataSize);
    }
}

FrameData::~FrameData() = default;

const std::vector<DecodedMask> &
FrameData::getDecodedMasks() {
    if (!masksDecoded_) {
        decodeRleMasks();
        masksDecoded_ = true;
    }
    return decodedMasks_;
}

const std::vector<uint8_t> &
FrameData::getMergedTexture() {
    if (!textureMerged_) {
        if (!masksDecoded_) {
            decodeRleMasks();
            masksDecoded_ = true;
        }
        mergeMasksToTexture();
        textureMerged_ = true;
    }
    return mergedTexture_;
}

void
FrameData::decodeRleMasks() {
    decodedMasks_.clear();

    if (maskData_.empty()) {
        return;
    }

    // 解析mask头部
    const MaskHeader *header = reinterpret_cast<const MaskHeader *>(maskData_.data());
    if (header->maskCount == 0) {
        return;
    }

    // 获取mask信息数组
    const MaskInfo *maskInfos = reinterpret_cast<const MaskInfo *>(
        maskData_.data() + sizeof(MaskHeader)
    );

    // 获取RLE数据区域起始位置
    const uint8_t *rleDataRegion = maskData_.data() +
                                   sizeof(MaskHeader) + header->maskCount * sizeof(MaskInfo);

    // 解码每个mask
    for (uint32_t i = 0; i < header->maskCount; ++i) {
        const MaskInfo &info = maskInfos[i];

        // 获取该mask的RLE数据（COCO格式）
        const uint8_t *cocoRleData = rleDataRegion + info.rleOffset;

        // COCO RLE数据长度（Python端现在直接传递字节长度）
        size_t dataLength = info.rleCountsLength;

        // 使用COCO RLE解码器解码为mask数据，填充tissue_id值
        std::vector<uint8_t> maskData = RleDecoder::decodeCocoRleToMask(
            cocoRleData,
            dataLength,
            info.originalWidth,
            info.originalHeight,
            info.tissueId
        );

        // 添加到解码结果
        DecodedMask decodedMask;
        decodedMask.objectId = info.objectId;
        decodedMask.width = info.originalWidth;
        decodedMask.height = info.originalHeight;
        decodedMask.tissueId = info.tissueId;
        decodedMask.maskData = std::move(maskData);

        decodedMasks_.push_back(std::move(decodedMask));
    }
}

void
FrameData::mergeMasksToTexture() {
    if (maskData_.empty()) {
        return;
    }

    // 获取mask头部信息
    const MaskHeader *header = reinterpret_cast<const MaskHeader *>(maskData_.data());

    // 准备mask数据用于合并
    std::vector<std::vector<uint8_t>> maskDataList;
    for (const auto &mask : decodedMasks_) {
        maskDataList.push_back(mask.maskData);
    }

    // 合并masks到纹理，使用真实的mask尺寸
    mergedTexture_ = RleDecoder::mergeMasksToTexture(
        maskDataList,
        header->maskWidth,
        header->maskHeight
    );
}

// MaskIpc实现
MaskIpc::MaskIpc()
    : shmHandler_(nullptr), shmFd_(-1), running_(false), framesReceived_(0), framesDropped_(0) {
}

MaskIpc::~MaskIpc() {
    cleanup();
}

bool
MaskIpc::initialize(const std::string &shmName) {
    shmName_ = shmName;

    if (!createSharedMemory()) {
        LOG_E("{}", "Failed to create shared memory");
        return false;
    }

    if (!mapSharedMemory()) {
        LOG_E("{}", "Failed to map shared memory");
        cleanup();
        return false;
    }

    initializeSharedMemory();
    running_ = true;

    LOG_I("MaskIpc initialized with shared memory: {}", shmName_);
    return true;
}

void
MaskIpc::cleanup() {
    running_ = false;

    if (shmHandler_) {
        munmap(shmHandler_, TOTAL_SHM_SIZE);
        shmHandler_ = nullptr;
    }

    if (shmFd_ >= 0) {
        close(shmFd_);
        shmFd_ = -1;
    }

    LOG_I("{}", "MaskIpc cleanup completed");
}

bool
MaskIpc::createSharedMemory() {
    // 创建共享内存
    shmFd_ = shm_open(shmName_.c_str(), O_CREAT | O_RDWR, 0666);
    if (shmFd_ < 0) {
        LOG_E("Failed to create shared memory: {}", strerror(errno));
        return false;
    }

    // 设置大小
    if (ftruncate(shmFd_, TOTAL_SHM_SIZE) < 0) {
        LOG_E("Failed to set shared memory size: {}", strerror(errno));
        return false;
    }

    return true;
}

bool
MaskIpc::mapSharedMemory() {
    shmHandler_ = static_cast<SharedMemory *>(
        mmap(nullptr, TOTAL_SHM_SIZE, PROT_READ | PROT_WRITE, MAP_SHARED, shmFd_, 0)
    );

    if (shmHandler_ == MAP_FAILED) {
        LOG_E("Failed to map shared memory: {}", strerror(errno));
        shmHandler_ = nullptr;
        return false;
    }

    return true;
}

void
MaskIpc::initializeSharedMemory() {
    // 初始化头部信息
    shmHandler_->magicNumber = MAGIC_NUMBER;
    shmHandler_->version = VERSION;
    shmHandler_->bufferCount = 4;
    shmHandler_->writeIndex = 0;
    shmHandler_->readIndex = 0;
    shmHandler_->reserved = 0;

    // 初始化统计信息
    shmHandler_->framesProduced = 0;
    shmHandler_->framesConsumed = 0;
    shmHandler_->framesDropped = 0;

    // 初始化每个缓冲区
    for (int i = 0; i < 4; ++i) {
        shmHandler_->buffers[i].state = BufferState::Empty;
        shmHandler_->buffers[i].maskDataSize = 0;
        shmHandler_->buffers[i].reserved = 0;
        std::memset(&shmHandler_->buffers[i].metadata, 0, sizeof(FrameMetadata));
    }
}

bool
MaskIpc::hasAvailableFrame() const {
    if (!running_ || !shmHandler_ || shmHandler_->magicNumber != MAGIC_NUMBER) {
        return false;
    }

    uint32_t readIndex = shmHandler_->readIndex % 4;
    return shmHandler_->buffers[readIndex].state == BufferState::Ready;
}

uint32_t
MaskIpc::getAvailableFrameCount() const {
    if (!running_ || !shmHandler_ || shmHandler_->magicNumber != MAGIC_NUMBER) {
        return 0;
    }

    uint32_t count = 0;
    for (int i = 0; i < 4; ++i) {
        if (shmHandler_->buffers[i].state == BufferState::Ready) {
            count++;
        }
    }
    return count;
}

std::unique_ptr<FrameData>
MaskIpc::tryReceiveFrame() {
    if (!running_ || !shmHandler_ || shmHandler_->magicNumber != MAGIC_NUMBER) {
        return nullptr;
    }

    uint32_t readIndex = shmHandler_->readIndex % 4;
    FrameBuffer &buffer = shmHandler_->buffers[readIndex];

    // 快速检查，不修改状态
    if (buffer.state != BufferState::Ready) {
        return nullptr;
    }

    // 1. 先将状态改为Reading，阻止写入端访问
    buffer.state = BufferState::Reading;

    // 2. 记录元数据快照用于验证
    FrameMetadata metadataSnapshot = buffer.metadata;
    uint32_t maskSizeSnapshot = buffer.maskDataSize;

    // 3. 拷贝数据
    auto frameData = std::make_unique<FrameData>(
        buffer.metadata,
        buffer.imageData,
        buffer.maskData,
        buffer.maskDataSize
    );

    // 4. 验证数据在拷贝过程中是否被修改（检查元数据一致性）
    if (buffer.metadata.frameId != metadataSnapshot.frameId ||
        buffer.metadata.timestamp != metadataSnapshot.timestamp ||
        buffer.maskDataSize != maskSizeSnapshot) {
        // 数据在拷贝期间被修改，丢弃这帧，但仍需正确处理状态
        LOG_W("Frame {} data changed during copy, discarding", metadataSnapshot.frameId);
        buffer.state = BufferState::Processed; // 标记为已处理，允许重新使用
        {
            std::lock_guard<std::mutex> lock(statsMutex_);
            framesDropped_++;
        }
        return nullptr;
    }

    // 5. 拷贝成功，标记为已处理
    buffer.state = BufferState::Processed;
    shmHandler_->readIndex++;
    shmHandler_->framesConsumed++;

    {
        std::lock_guard<std::mutex> lock(statsMutex_);
        framesReceived_++;
    }

    return frameData;
}

bool
MaskIpc::isInitialized() const {
    return shmHandler_ != nullptr &&
           shmHandler_->magicNumber == MAGIC_NUMBER &&
           running_;
}

uint64_t
MaskIpc::getFramesReceived() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return framesReceived_;
}

uint64_t
MaskIpc::getFramesDropped() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return framesDropped_;
}

} // namespace MK