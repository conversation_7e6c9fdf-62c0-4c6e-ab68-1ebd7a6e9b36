#include "codec/video_encoder.hpp"

#include "utils/logger.hpp"

extern "C" {
#include <libavutil/imgutils.h>
}

namespace MK {

VideoEncoder::VideoEncoder(
    const PixelFormat pixelFormat,
    const int width,
    const int height,
    const int framerate,
    const int bitrate,
    const CallBack didEncode
)
    : didEncode(didEncode),
      pixelFormat(pixelFormat),
      width(width),
      height(height),
      framerate(framerate),
      bitrate(bitrate) {}

VideoEncoder::~VideoEncoder() {
    if (codecContext) {
        avcodec_free_context(&codecContext);
    }
    if (avFrame) {
        av_frame_free(&avFrame);
    }
    if (avPacket) {
        av_packet_free(&avPacket);
    }
    LOG_I("{}", "VideoEncoder destroyed");
}

bool
VideoEncoder::init() {
    if (codecContext) {
        return true;
    }

    const AVCodec *codec = avcodec_find_encoder_by_name("h264_nvenc");
    if (!codec) {
        LOG_W("{}", "Hardware encoder 'h264_nvenc' not found. Trying 'libx264' software encoder.");
        codec = avcodec_find_encoder_by_name("libx264");
        if (!codec) {
            LOG_E("{}", "Could not find 'libx264' encoder.");
            return false;
        }
    }

    codecContext = avcodec_alloc_context3(codec);
    if (!codecContext) {
        LOG_E("{}", "Could not allocate video codec context");
        return false;
    }

    codecContext->width = width;
    codecContext->height = height;
    codecContext->bit_rate = bitrate * 1000;
    codecContext->time_base = {1, 1000}; // PTS in milliseconds
    codecContext->framerate = {framerate, 1};
    codecContext->gop_size = framerate; // I-frame interval
    codecContext->max_b_frames = 0;     // No B-frames for low latency
    codecContext->pix_fmt = (pixelFormat == PixelFormat_nv12) ? AV_PIX_FMT_NV12 : AV_PIX_FMT_YUV420P;

    if (strcmp(codec->name, "h264_nvenc") == 0) {
        av_opt_set(codecContext->priv_data, "preset", "p1", 0);
        av_opt_set(codecContext->priv_data, "tune", "ll", 0);
        av_opt_set(codecContext->priv_data, "rc", "cbr", 0);
        LOG_I("{}", "Using hardware encoder: h264_nvenc");
    } else if (strcmp(codec->name, "libx264") == 0) {
        av_opt_set(codecContext->priv_data, "preset", "ultrafast", 0);
        av_opt_set(codecContext->priv_data, "tune", "zerolatency", 0);
        LOG_I("{}", "Using software encoder: libx264");
    }

    if (avcodec_open2(codecContext, codec, nullptr) < 0) {
        LOG_E("{}", "Could not open codec");
        return false;
    }

    avFrame = av_frame_alloc();
    if (!avFrame) {
        LOG_E("{}", "Could not allocate video frame");
        return false;
    }
    avFrame->format = codecContext->pix_fmt;
    avFrame->width = width;
    avFrame->height = height;

    if (av_frame_get_buffer(avFrame, 0) < 0) {
        LOG_E("{}", "Could not allocate the video frame data");
        return false;
    }

    avPacket = av_packet_alloc();
    if (!avPacket) {
        LOG_E("{}", "Could not allocate packet");
        return false;
    }

    LOG_I("{}", "FFmpeg VideoEncoder initialized successfully.");
    return true;
}

bool
VideoEncoder::encode(const uint8_t *const frame, long frameLen, int64_t pts_us) {
    if (!codecContext) {
        LOG_E("{}", "Encoder is not initialized!");
        return false;
    }

    if (av_frame_make_writable(avFrame) < 0) {
        LOG_E("{}", "Frame not writable");
        return false;
    }

    const int y_size = width * height;
    if (avFrame->format == AV_PIX_FMT_YUV420P) {
        const int uv_size = y_size / 4;
        memcpy(avFrame->data[0], frame, y_size);
        memcpy(avFrame->data[1], frame + y_size, uv_size);
        memcpy(avFrame->data[2], frame + y_size + uv_size, uv_size);
    } else if (avFrame->format == AV_PIX_FMT_NV12) {
        const int uv_size = y_size / 2;
        memcpy(avFrame->data[0], frame, y_size);
        memcpy(avFrame->data[1], frame + y_size, uv_size);
    } else {
        LOG_E("Unsupported pixel format for encoding: {}", avFrame->format);
        return false;
    }

    avFrame->pts = pts_us / 1000; // Convert microseconds to milliseconds for the PTS

    int ret = avcodec_send_frame(codecContext, avFrame);
    if (ret < 0) {
        char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
        av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
        LOG_E("Error sending a frame for encoding: {}", errbuf);
        return false;
    }

    while (ret >= 0) {
        ret = avcodec_receive_packet(codecContext, avPacket);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
            return true;
        } else if (ret < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0};
            av_strerror(ret, errbuf, AV_ERROR_MAX_STRING_SIZE);
            LOG_E("Error during encoding: {}", errbuf);
            return false;
        }

        if (avPacket->duration <= 0) {
            avPacket->duration = 1000 / framerate;
        }

        didEncode(avPacket->data, avPacket->size, avPacket->pts, avPacket->duration, avPacket->flags & AV_PKT_FLAG_KEY);
        av_packet_unref(avPacket);
    }

    return true;
}

} // namespace MK