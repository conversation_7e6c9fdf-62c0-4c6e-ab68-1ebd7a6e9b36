# The Flutter tooling requires that developers have CMake 3.10 or later
# installed. You should not increase this version, as doing so will cause
# the plugin to fail to compile for some customers of the plugin.
cmake_minimum_required(VERSION 3.10)

# Project-level configuration.
set(PROJECT_NAME "medias_kit")
set(CMAKE_CXX_STANDARD 17)
project(${PROJECT_NAME} LANGUAGES C CXX)

# This value is used when generating builds using this plugin, so it must
# not be changed.
set(PLUGIN_NAME "medias_kit_plugin")

add_definitions(-DCURRENT_BUILD_TYPE="${CMAKE_BUILD_TYPE}")
message(STATUS ">>>>>>>>>>> CURRENT_BUILD_TYPE: ${CMAKE_BUILD_TYPE}")

# 读取环境变量
if(DEFINED ENV{FLUTTER_ROOT})
  set(DART_SDK "$ENV{FLUTTER_ROOT}/bin/cache/dart-sdk")
  message(STATUS ">>>>>>>>>>> DART_SDK: ${DART_SDK}")
  # 添加 dart sdk include 路径, 用于 C async call Dart 
  # target_include_directories(${PLUGIN_NAME} PUBLIC "${DART_SDK}/include")
  # target_sources(${PLUGIN_NAME} PRIVATE "${DART_SDK}/include/dart_api_dl.c")
else()
  message(FATAL_ERROR "FLUTTER_ROOT environment variable not set")
endif()

# Any new source files that you add to the plugin should be added here.
file(GLOB_RECURSE PLUGIN_SOURCES
  "source/*.cc"
  "source/*.cpp"
  "${DART_SDK}/include/dart_api_dl.c"
)

# Define the plugin library target. Its name must not be changed (see comment
# on PLUGIN_NAME above).
add_library(${PLUGIN_NAME} SHARED ${PLUGIN_SOURCES})

# Apply a standard set of build settings that are configured in the
# application-level CMakeLists.txt. This can be removed for plugins that want
# full control over build settings.
apply_standard_settings(${PLUGIN_NAME})

# Symbols are hidden by default to reduce the chance of accidental conflicts
# between plugins. This should not be removed; any symbols that should be
# exported should be explicitly exported with the FLUTTER_PLUGIN_EXPORT macro.
set_target_properties(${PLUGIN_NAME} PROPERTIES CXX_VISIBILITY_PRESET hidden)
target_compile_definitions(${PLUGIN_NAME} PRIVATE FLUTTER_PLUGIN_IMPL)

# Source include directories and library dependencies. Add any plugin-specific
# dependencies here.
target_include_directories(${PLUGIN_NAME} PUBLIC "${DART_SDK}/include")
target_include_directories(${PLUGIN_NAME} PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/include/private")
target_include_directories(${PLUGIN_NAME} PUBLIC "${CMAKE_CURRENT_SOURCE_DIR}/include/public")
target_link_libraries(${PLUGIN_NAME} PRIVATE flutter)
target_link_libraries(${PLUGIN_NAME} PRIVATE PkgConfig::GTK)

# 指定 SDL2 包含路径, 用 #include <SDL.h> 替换 #include <SDL2/SDL.h>
# 解决 included file: 'begin_code.h' file not found 问题, 官方说明 SDL2 的bug, 在SDL3中已经修复
# find_package(SDL2 REQUIRED)
# include_directories(PRIVATE ${SDL2_INCLUDE_DIRS})

# OpenCV SDK
find_package(OpenCV REQUIRED)
target_include_directories(${PLUGIN_NAME} PRIVATE ${OpenCV_INCLUDE_DIRS})

# FFmpeg 库支持
find_package(PkgConfig REQUIRED)
pkg_check_modules(FFMPEG REQUIRED 
    libavcodec>=58.0.0
    libavutil>=56.0.0
    libavformat>=58.0.0
)
target_include_directories(${PLUGIN_NAME} PRIVATE ${FFMPEG_INCLUDE_DIRS})
target_link_directories(${PLUGIN_NAME} PRIVATE ${FFMPEG_LIBRARY_DIRS})

# BLE SDK
include_directories(PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/bluez_inc/include")
target_link_directories(${PLUGIN_NAME} PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/bluez_inc/libs")

# 美乐威 SDK (仅保留采集相关功能)
include_directories(PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/mw_sdk/include")
target_link_directories(${PLUGIN_NAME} PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/mw_sdk/libs")

# 声网 SDK
include_directories(PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/agora_sdk/include")
target_link_directories(${PLUGIN_NAME} PUBLIC "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/agora_sdk/libs")

# 讯飞 SDK
include_directories(PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/xfyun_sdk/include")
target_link_directories(${PLUGIN_NAME} PUBLIC "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/xfyun_sdk/libs")

# 私有依赖库列表 - 移除mw_venc编码库，添加FFmpeg
list(APPEND DEPENDENCY_PRIVATE_LIBS
  ${FFMPEG_LIBRARIES} x264 fdk-aac pci asound pulse pulse-simple udev v4l2 epoxy dl z resolv va-drm va fmt spdlog ${OpenCV_LIBS}
  MWCapture mw_mp4 Binc
)

# 公共依赖库列表, 自引三方动态库需要 PUBLIC 方式链接 rpath 相对路径, 否则打包出来会找不到
list(APPEND DEPENDENCY_PUBLIC_LIBS
  aosl agora-rtc-sdk agora-fdkaac
  aikit msc
)

target_link_libraries(${PLUGIN_NAME} PRIVATE ${DEPENDENCY_PRIVATE_LIBS})
target_link_libraries(${PLUGIN_NAME} PUBLIC ${DEPENDENCY_PUBLIC_LIBS})

set(INSTALL_BUNDLE_LIB_DIR "${CMAKE_BINARY_DIR}/bundle/lib")
set(INSTALL_BUNDLE_MSC_DIR "${CMAKE_BINARY_DIR}/bundle/msc")

file(GLOB_RECURSE AGORA_LIBS "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/agora_sdk/libs/*.so")
file(GLOB_RECURSE XFYUN_LIBS "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/xfyun_sdk/libs/*.so")
# file(GLOB_RECURSE XFYUN_CFG "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/xfyun_sdk/msc.cfg")


# 自引三方动态库安装路径指定, 否则打包时不会导出(导出到 bunlde/lib)
install(FILES ${AGORA_LIBS} DESTINATION ${INSTALL_BUNDLE_LIB_DIR} COMPONENT Runtime)
install(FILES ${XFYUN_LIBS} DESTINATION ${INSTALL_BUNDLE_LIB_DIR} COMPONENT Runtime)
# install(FILES ${XFYUN_CFG} DESTINATION ${INSTALL_BUNDLE_MSC_DIR})

# List of absolute paths to libraries that should be bundled with the plugin.
# This list could contain prebuilt libraries, or libraries created by an
# external build triggered from this build file.
set(medias_kit_bundled_libraries
  ""
  PARENT_SCOPE
)

# 下方测试集需要访问 github 经常失败, 暂时关闭
return()

# === Tests ===
# These unit tests can be run from a terminal after building the example.

# Only enable test builds when building the example (which sets this variable)
# so that plugin clients aren't building the tests.
if (${include_${PROJECT_NAME}_tests})
if(${CMAKE_VERSION} VERSION_LESS "3.11.0")
message("Unit tests require CMake 3.11.0 or later")
else()
set(TEST_RUNNER "${PROJECT_NAME}_test")
enable_testing()

# Add the Google Test dependency.
include(FetchContent)
FetchContent_Declare(
  googletest
  URL https://github.com/google/googletest/archive/release-1.11.0.zip
)
# Prevent overriding the parent project's compiler/linker settings
set(gtest_force_shared_crt ON CACHE BOOL "" FORCE)
# Disable install commands for gtest so it doesn't end up in the bundle.
set(INSTALL_GTEST OFF CACHE BOOL "Disable installation of googletest" FORCE)

FetchContent_MakeAvailable(googletest)

# The plugin's exported API is not very useful for unit testing, so build the
# sources directly into the test binary rather than using the shared library.
add_executable(${TEST_RUNNER}
  test/medias_kit_plugin_test.cc
  ${PLUGIN_SOURCES}
)
apply_standard_settings(${TEST_RUNNER})
target_include_directories(${TEST_RUNNER} PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}")
target_link_libraries(${TEST_RUNNER} PRIVATE flutter)
target_link_libraries(${TEST_RUNNER} PRIVATE PkgConfig::GTK)
target_link_libraries(${TEST_RUNNER} PRIVATE gtest_main gmock)

target_include_directories(${TEST_RUNNER} PRIVATE
  "${CMAKE_CURRENT_SOURCE_DIR}/include/private"
  "${CMAKE_CURRENT_SOURCE_DIR}/include/public"
)

target_link_directories(${TEST_RUNNER} PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/bluez_inc/libs")
target_link_directories(${TEST_RUNNER} PRIVATE "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/mw_sdk/libs")
target_link_directories(${TEST_RUNNER} PUBLIC "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/agora_sdk/libs")
target_link_directories(${TEST_RUNNER} PUBLIC "${CMAKE_CURRENT_SOURCE_DIR}/dependencies/xfyun_sdk/libs")

target_link_libraries(${TEST_RUNNER} PRIVATE ${DEPENDENCY_PRIVATE_LIBS})
target_link_libraries(${TEST_RUNNER} PUBLIC ${DEPENDENCY_PUBLIC_LIBS})

# Enable automatic test discovery.
include(GoogleTest)
gtest_discover_tests(${TEST_RUNNER})

endif()  # CMake version check
endif()  # include_${PROJECT_NAME}_tests