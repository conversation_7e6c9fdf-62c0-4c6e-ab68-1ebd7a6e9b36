#!/usr/bin/env python3
"""
30FPS持续写入测试demo

使用OpenCV从摄像头/dev/video0读取真实视频帧, 向Flutter应用发送帧数据和mask数据
"""

import time
import cv2
import numpy as np
from mask_ipc import MaskIpc


def main():
    """从摄像头30FPS持续写入测试"""
    print("启动OpenCV摄像头30FPS持续写入测试...")
    print("按Ctrl+C停止")

    # 初始化摄像头
    cap = cv2.VideoCapture(0)  # 使用/dev/video0
    if not cap.isOpened():
        print("❌ 无法打开摄像头 /dev/video0")
        return

    # 设置摄像头参数为1080P
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1920)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 1080)
    cap.set(cv2.CAP_PROP_FPS, 30)

    # 获取实际设置的参数
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    actual_fps = cap.get(cv2.CAP_PROP_FPS)

    print(f"摄像头分辨率: {actual_width}x{actual_height}")
    print(f"摄像头FPS: {actual_fps}")

    # 创建固定的masks_info (永久使用)
    fixed_masks_info = [
        {
            "object_id": 1,
            "rle_data": b"^akc02aQ17L4M3N1O1N3N1O2N100O10000O100O100O1000000O10000O10O1000O100O101bQOBkM2il0<XUOb0gj0^OSUOi0lj0WOQUOm0oj0ROlTOT1Rk0mNjTO\\1Qk0dNlTO`1Sk0_NjTOf1Uk0ZNhTOj1Wk0VNfTOo1Xk0QNdTOT2[k0lMaTOZ2^k0fMZTOd2ck0]MVTOj2ik0VMPTOS3mk0nLmSOZ3Ql0X1oSO]Jjk0j500O1O1O1O6J2N100O2N3M2M2O2N2M3N5K1O2N2N2M5L2N3M2M2N4M2M3N3M3M1O1N2O3M3L4M3L3M3N2M3M3N2N2N3M104K1O1N6K2N2N2M3N3M2N2M2O1N104K3N2M4M7I2TYOWEdf0U;J00O100O1O1O100O2N1O001O1O1O1O1O2N1O1O1O1O2N2N2N2N1O2N3M1O1O1O2N3M1O2N1O4L2N2N3M2N2N3M4L1O2N3M3M3M2N2N2N3M1O2N2N1O1O1O1O4L2N2N3M2N2N2N4L4L3M6J3M3M3M2N5K2N1O2N3M1O2N6J2N2N2N2N5K1O1O00001O00006J5K3M1O2N2N3M2N1O3M2N1O1O2N2N3M3M2N3M2N3M2N1O00O100HSLdROn3Zm0VLdROj3Ym0ZLeROg3Xm0>1lKiROe3[m0VLhROh3Zm0ULiROi3Xm0ULjROj3dm0N2N2N2N2N2N1O1O1O2N1O2N1O7I2N2N2N1O2N1O001O1O00001O001O1O001O002N1O2N3M2N3M3M5K1O2N1O2_NooNW1SP1eNRPOX1YP1N2N2N2N0O2O001O0O2O002M6K2Nh[[Q1",
            "original_width": 1920,
            "original_height": 1080,
            "tissue_id": 1,
        }
    ]

    fps = 30
    frame_interval = 1.0 / fps

    try:
        with MaskIpc("/mask_algorithm_data") as ipc:
            if not ipc.initialize():
                print("❌ 连接失败，请确保Flutter应用已启动")
                return

            print("✅ 连接成功，开始从摄像头30FPS持续写入...")

            frame_id = 0
            start_time = time.time()
            last_stats_time = start_time

            try:
                while True:
                    frame_start = time.time()

                    # 从摄像头读取帧
                    ret, frame = cap.read()
                    if not ret:
                        print("❌ 读取摄像头帧失败，跳过")
                        continue

                    # 获取帧大小并转换格式
                    height, width = frame.shape[:2]
                    frame_rgba = cv2.cvtColor(frame, cv2.COLOR_BGR2RGBA)
                    image_data = frame_rgba.tobytes()

                    # 发送帧数据 (使用预生成的固定mask)
                    success = ipc.write_frame(
                        image_data, width, height, fixed_masks_info
                    )

                    if success:
                        frame_id += 1

                        # 每秒显示一次统计
                        current_time = time.time()
                        if current_time - last_stats_time >= 1.0:
                            elapsed = current_time - start_time
                            actual_fps = frame_id / elapsed if elapsed > 0 else 0

                            stats = ipc.get_stats()
                            print(
                                f"[{int(elapsed)}s] 帧:{frame_id} 实际FPS:{actual_fps:.1f} "
                                f"生产:{stats['frames_produced']} 丢弃:{stats['frames_dropped']}"
                            )

                            last_stats_time = current_time
                    else:
                        print(f"❌ 帧 {frame_id} 发送失败")

                    # 精确控制帧率
                    frame_time = time.time() - frame_start
                    sleep_time = max(0, frame_interval - frame_time)
                    if sleep_time > 0:
                        time.sleep(sleep_time)

            except KeyboardInterrupt:
                print("\n🛑 停止发送...")

            # 最终统计
            total_time = time.time() - start_time
            final_fps = frame_id / total_time if total_time > 0 else 0
            stats = ipc.get_stats()

            print(f"\n📊 最终统计:")
            print(f"总帧数: {frame_id}")
            print(f"总时间: {total_time:.2f}秒")
            print(f"平均FPS: {final_fps:.1f}")
            print(f"详细统计: {stats}")

    finally:
        # 释放摄像头
        cap.release()
        print("🔌 摄像头已释放")


if __name__ == "__main__":
    main()
