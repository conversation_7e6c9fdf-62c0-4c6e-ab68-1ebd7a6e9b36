# Mask IPC Python工具

Python版本的Mask IPC工具，用于算法进程向Flutter应用发送实时帧数据和mask数据。

## 功能特性

- **高性能IPC通信**: 基于共享内存的零拷贝数据传输
- **实时数据传输**: 支持30fps实时视频和mask数据流
- **多mask支持**: 单帧可传输多个mask对象
- **RLE编码兼容**: 与C++端RLE解码器完全兼容
- **线程安全**: 支持多线程环境下的安全操作
- **资源管理**: 自动内存管理和资源清理

## 安装依赖

```bash
# 基本依赖
pip install numpy

# 示例程序额外依赖（可选）
pip install opencv-python
```

## 快速开始

### 1. 基本使用

```python
from mask_ipc import MaskIpc, MaskEncoder
import numpy as np

# 创建MaskIpc实例
shm_name = "/mask_algorithm_data"  # 需要与Flutter端保持一致
mask_ipc = MaskIpc(shm_name)

# 初始化连接
if mask_ipc.initialize():
    # 准备图像数据（RGBA格式）
    width, height = 640, 480
    image_data = np.random.randint(0, 255, (height, width, 4), dtype=np.uint8)
    
    # 准备mask数据
    masks = [
        {
            'object_id': 1,
            'mask_data': np.random.randint(0, 2, (height, width), dtype=np.uint8),
            'width': width,
            'height': height,
            'tissue_id': 1,
        }
    ]
    
    # 编码mask数据
    encoded_mask = MaskEncoder.encode_masks(masks)
    
    # 发送帧数据
    success = mask_ipc.write_frame(
        image_data.tobytes(), width, height, encoded_mask
    )
    
    if success:
        print("帧数据发送成功")
    
    # 清理资源
    mask_ipc.cleanup()
```

### 2. 使用上下文管理器（推荐）

```python
with MaskIpc("/mask_algorithm_data") as mask_ipc:
    if mask_ipc.initialize():
        # 发送数据...
        pass
    # 自动清理资源
```

## API 文档

### MaskIpc类

主要的IPC管理类，负责与Flutter应用的共享内存通信。

#### 构造函数

```python
MaskIpc(shm_name: str)
```

- `shm_name`: 共享内存名称，需要与Flutter端保持一致

#### 主要方法

- `initialize() -> bool`: 初始化共享内存连接
- `write_frame(image_data, width, height, mask_data, timestamp=None) -> bool`: 写入帧数据
- `get_stats() -> dict`: 获取统计信息
- `cleanup()`: 清理资源
- `is_initialized() -> bool`: 检查初始化状态

### MaskEncoder类

静态类，用于将mask数据编码为与C++端兼容的RLE格式。

#### 主要方法

```python
MaskEncoder.encode_masks(masks: List[dict]) -> bytes
```

编码多个mask为二进制数据。

mask字典格式:
```python
{
    'object_id': int,        # 对象ID
    'mask_data': numpy.ndarray,  # mask数据（二值化）
    'width': int,            # 宽度
    'height': int,           # 高度  
    'tissue_id': int,        # 组织类型ID
}
```

## 数据格式说明

### 图像数据格式

- **格式**: RGBA
- **类型**: uint8
- **大小限制**: 最大8,294,400字节 (1920x1080x4)
- **内存布局**: 行优先存储

### Mask数据格式

- **编码**: RLE (Run-Length Encoding)  
- **兼容性**: 与SAM2标准RLE格式兼容
- **存储顺序**: 列优先遍历
- **大小限制**: 最大2MB编码数据

### 共享内存布局

```
SharedMemory {
    header: {
        magicNumber: 0x4D4B5348 ('MKSH')
        version: 1
        bufferCount: 4
        writeIndex: uint32
        readIndex: uint32
        statistics...
    }
    buffers[4]: {
        metadata: FrameMetadata
        state: BufferState
        imageData[8294400]: uint8
        maskData[2097152]: uint8
    }
}
```

## 运行示例

### 测试静态帧发送

```bash
cd ipc_tool
python example_usage.py
# 选择选项 1
```

### 模拟实时视频流

```bash
cd ipc_tool  
python example_usage.py
# 选择选项 2
# 按 Ctrl+C 停止
```

### 测试Mask编码

```bash
cd ipc_tool
python example_usage.py  
# 选择选项 3
```

## 注意事项

### 启动顺序

1. **先启动Flutter应用**: Flutter应用负责创建和初始化共享内存
2. **再启动Python算法进程**: Python端连接到已存在的共享内存

### 性能优化建议

- 使用合适的图像分辨率，避免超过缓冲区大小限制
- 及时清理资源，避免内存泄漏
- 在高频发送场景下，监控帧丢失率
- 考虑使用多线程分离数据处理和发送逻辑

### 错误处理

常见错误和解决方案:

1. **"无法连接到共享内存"**: 确保Flutter应用已启动并初始化了MaskIpc
2. **"魔数校验失败"**: 确保Flutter端和Python端使用相同版本的协议
3. **"没有可用的缓冲区"**: 降低发送频率或检查Flutter端是否正常接收数据

### 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 获取统计信息
stats = mask_ipc.get_stats()
print(f"生产帧数: {stats['frames_produced']}")
print(f"丢弃帧数: {stats['frames_dropped']}")
```

## 与Flutter端协作

### Flutter端初始化

```dart
// 创建MaskIpc实例，提供颜色数组用于mask渲染
final maskIpc = MaskIpc('/mask_algorithm_data', [
  0xFFFF0000, // 红色 - 对应tissue_id=1
  0xFF00FF00, // 绿色 - 对应tissue_id=2  
  0xFF0000FF, // 蓝色 - 对应tissue_id=3
  // ...更多颜色
]);

// 初始化并启动
maskIpc.initialize();
maskIpc.start();
```

### 颜色映射

Python端的`tissue_id`会映射到Flutter端提供的颜色数组:
- tissue_id=1 → colors[0] (红色)
- tissue_id=2 → colors[1] (绿色)  
- tissue_id=3 → colors[2] (蓝色)

## 许可证

本项目遵循与主项目相同的许可证。