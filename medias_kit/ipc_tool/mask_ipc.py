"""
Mask IPC - Python版本写入端

用于算法进程将帧数据和编码的mask数据写入共享内存的工具类
与Flutter应用进行高性能IPC通信, 支持60fps内实时传输
"""

import ctypes
import mmap
import time
from enum import IntEnum
from typing import Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class BufferState(IntEnum):
    """缓冲区状态枚举"""

    EMPTY = 0  # 空闲状态
    WRITING = 1  # 算法进程写入中
    READY = 2  # 数据就绪，待app读取
    READING = 3  # app读取中
    PROCESSED = 4  # app处理完成，可释放


class FrameMetadata(ctypes.Structure):
    """帧元数据结构"""

    _pack_ = 1
    _fields_ = [
        ("timestamp", ctypes.c_int64),  # 时间戳（毫秒）
        ("frame_id", ctypes.c_uint32),  # 帧序号 (C++: frameId)
        ("width", ctypes.c_uint32),  # 图像宽度
        ("height", ctypes.c_uint32),  # 图像高度
        ("image_size", ctypes.c_uint32),  # 图像数据大小 (C++: imageSize)
        ("mask_size", ctypes.c_uint32),  # mask数据大小 (C++: maskSize)
        ("reserved", ctypes.c_uint32),  # 内存对齐填充
    ]


class MaskInfo(ctypes.Structure):
    """Mask信息结构 - 对应C++端MaskInfo"""

    _pack_ = 1
    _fields_ = [
        ("object_id", ctypes.c_uint32),  # 对象ID (C++: objectId)
        ("rle_offset", ctypes.c_uint32),  # RLE数据偏移量 (C++: rleOffset)
        (
            "rle_counts_length",
            ctypes.c_uint32,
        ),  # RLE counts数组长度 (C++: rleCountsLength)
        ("original_width", ctypes.c_uint32),  # 原始mask宽度 (C++: originalWidth)
        ("original_height", ctypes.c_uint32),  # 原始mask高度 (C++: originalHeight)
        ("tissue_id", ctypes.c_uint32),  # 组织ID (C++: tissueId)
    ]


class MaskHeader(ctypes.Structure):
    """Mask头部信息结构 - 对应C++端MaskHeader"""

    _pack_ = 1
    _fields_ = [
        ("mask_count", ctypes.c_uint32),  # mask数量 (C++: maskCount)
        ("mask_width", ctypes.c_uint32),  # mask宽度 (C++: maskWidth)
        ("mask_height", ctypes.c_uint32),  # mask高度 (C++: maskHeight)
        ("reserved", ctypes.c_uint32),  # 填充到16字节
    ]


class MaskData(ctypes.Structure):
    """完整的Mask数据结构 - 包含头部、信息数组和RLE数据

    实际布局:
    - MaskHeader: 头部信息
    - MaskInfo数组: 每个mask的信息
    - RLE数据区域: 实际编码数据
    """

    _pack_ = 1
    _fields_ = [
        ("header", MaskHeader),
        # 注意: 实际使用时需要根据maskCount动态分配MaskInfo数组和RLE数据
        # 这里只是结构定义，实际数据通过bytes方式传输
    ]


class FrameBuffer(ctypes.Structure):
    """帧缓冲区结构"""

    _pack_ = 1
    _fields_ = [
        ("metadata", FrameMetadata),
        ("state", ctypes.c_uint32),  # BufferState枚举值
        ("mask_data_size", ctypes.c_uint32),  # mask数据大小 (C++: maskDataSize)
        ("reserved", ctypes.c_uint32),  # 内存对齐填充
        ("image_data", ctypes.c_uint8 * 8294400),  # 1920x1080x4 RGBA (C++: imageData)
        ("mask_data", ctypes.c_uint8 * 2097152),  # 2MB固定mask区域 (C++: maskData)
    ]


class SharedMemory(ctypes.Structure):
    """共享内存结构"""

    _pack_ = 1
    _fields_ = [
        (
            "magic_number",
            ctypes.c_uint32,
        ),  # 魔数校验: 0x4D4B5348('MKSH') (C++: magicNumber)
        ("version", ctypes.c_uint32),  # 版本号
        ("buffer_count", ctypes.c_uint32),  # 缓冲区数量(4) (C++: bufferCount)
        ("write_index", ctypes.c_uint32),  # 写入索引 (C++: writeIndex)
        ("read_index", ctypes.c_uint32),  # 读取索引 (C++: readIndex)
        ("reserved", ctypes.c_uint32),  # 内存对齐填充
        # 统计信息
        ("frames_produced", ctypes.c_uint64),  # 生产帧数 (C++: framesProduced)
        ("frames_consumed", ctypes.c_uint64),  # 消费帧数 (C++: framesConsumed)
        ("frames_dropped", ctypes.c_uint64),  # 丢弃帧数 (C++: framesDropped)
        ("buffers", FrameBuffer * 4),
    ]


class MaskIpc:
    """Mask IPC管理器 - Python版（写入端）

    用于算法进程向Flutter应用写入帧数据和编码的mask数据
    支持高性能共享内存IPC通信，理论上限60fps，实际速度取决于写入性能
    """

    MAGIC_NUMBER = 0x4D4B5348  # 'MKSH'
    VERSION = 1
    BUFFER_COUNT = 4
    MAX_IMAGE_SIZE = 8294400  # 1920x1080x4 RGBA
    MAX_MASK_SIZE = 2097152  # 2MB

    def __init__(self, shm_name: str):
        """初始化MaskIpc

        Args:
            shm_name: 共享内存名称，需要与Flutter端保持一致
        """
        self.shm_name = shm_name
        self.shm_fd = None
        self.shm_handler = None
        self.mmap_obj = None
        self.running = False
        self.frame_counter = 0
        # 无锁设计：单写单读场景，与Flutter端保持一致

        # 统计信息
        self.frames_produced = 0
        self.frames_dropped = 0

    def initialize(self) -> bool:
        """初始化共享内存连接

        Returns:
            bool: 初始化是否成功
        """
        try:
            if not self._create_shared_memory():
                logger.error("创建共享内存失败")
                return False

            if not self._map_shared_memory():
                logger.error("映射共享内存失败")
                self.cleanup()
                return False

            if not self._verify_shared_memory():
                logger.error("验证共享内存失败")
                self.cleanup()
                return False

            # 写入端连接后初始化所有缓冲区为空闲状态
            self._initialize_buffers()

            self.running = True
            logger.info(f"MaskIpc初始化成功，共享内存: {self.shm_name}")
            return True

        except Exception as e:
            logger.error(f"初始化异常: {e}")
            self.cleanup()
            return False

    def _create_shared_memory(self) -> bool:
        """创建或连接共享内存"""
        import os

        try:
            # 尝试打开现有共享内存
            self.shm_fd = os.open(f"/dev/shm{self.shm_name}", os.O_RDWR)
            logger.info("连接到现有共享内存")
            return True
        except (OSError, FileNotFoundError):
            logger.error(f"无法连接到共享内存 {self.shm_name}，请确保Flutter应用已启动")
            return False

    def _map_shared_memory(self) -> bool:
        """映射共享内存"""
        try:
            self.mmap_obj = mmap.mmap(
                self.shm_fd,
                ctypes.sizeof(SharedMemory),
                mmap.MAP_SHARED,
                mmap.PROT_READ | mmap.PROT_WRITE,
            )

            # 创建ctypes结构体映射
            self.shm_handler = SharedMemory.from_buffer(self.mmap_obj)
            return True

        except Exception as e:
            logger.error(f"映射共享内存失败: {e}")
            return False

    def _verify_shared_memory(self) -> bool:
        """验证共享内存格式"""
        if self.shm_handler.magic_number != self.MAGIC_NUMBER:
            logger.error(
                f"魔数校验失败: {self.shm_handler.magic_number:08X} != {self.MAGIC_NUMBER:08X}"
            )
            return False

        if self.shm_handler.version != self.VERSION:
            logger.error(f"版本不匹配: {self.shm_handler.version} != {self.VERSION}")
            return False

        if self.shm_handler.buffer_count != self.BUFFER_COUNT:
            logger.error(
                f"缓冲区数量不匹配: {self.shm_handler.buffer_count} != {self.BUFFER_COUNT}"
            )
            return False

        logger.info("共享内存格式验证成功")
        return True

    def _initialize_buffers(self) -> None:
        """初始化所有缓冲区为空闲状态"""
        initialized_count = 0
        for i in range(self.BUFFER_COUNT):
            old_state = self.shm_handler.buffers[i].state
            self.shm_handler.buffers[i].state = BufferState.EMPTY
            if old_state != BufferState.EMPTY:
                initialized_count += 1
                logger.info(f"缓冲区 {i}: {BufferState(old_state).name} -> EMPTY")

        if initialized_count > 0:
            logger.info(f"已初始化 {initialized_count} 个缓冲区为空闲状态")
        else:
            logger.info("所有缓冲区已处于空闲状态")

    def write_frame(
        self,
        image_data: bytes,
        width: int,
        height: int,
        masks_info: list,
        timestamp: Optional[int] = None,
    ) -> bool:
        """写入帧数据

        Args:
            image_data: 图像数据（RGBA格式）
            width: 图像宽度
            height: 图像高度
            masks_info: 算法进程提供的mask信息列表，每个元素包含:
                - object_id: 对象ID
                - rle_data: RLE编码数据（bytes）
                - original_width: 原始mask宽度
                - original_height: 原始mask高度
                - tissue_id: 组织ID
            timestamp: 时间戳（毫秒），None则使用当前时间

        Returns:
            bool: 写入是否成功
        """
        if not self.running or not self.shm_handler:
            logger.warning("MaskIpc未初始化或已停止")
            return False

        if len(image_data) > self.MAX_IMAGE_SIZE:
            logger.error(f"图像数据过大: {len(image_data)} > {self.MAX_IMAGE_SIZE}")
            return False

        # 构建完整的mask数据
        # 从第一个mask获取统一的尺寸 (假设同一帧内所有mask尺寸相同)
        if masks_info:
            mask_width = masks_info[0]["original_width"]
            mask_height = masks_info[0]["original_height"]
        else:
            mask_width = 0  # 没有mask时为0
            mask_height = 0

        encoded_mask_data = self._build_mask_data(masks_info, mask_width, mask_height)
        if encoded_mask_data is None:
            logger.error("_build_mask_data 返回 None，构建mask数据失败")
            return False

        if len(encoded_mask_data) > self.MAX_MASK_SIZE:
            logger.error(
                f"Mask数据过大: {len(encoded_mask_data)} > {self.MAX_MASK_SIZE}"
            )
            return False

        # 使用 writeIndex 确保顺序写入
        write_index = self.shm_handler.write_index % self.BUFFER_COUNT
        buffer_state = self.shm_handler.buffers[write_index].state

        # 检查目标缓冲区是否可用，遇到 READY 状态直接丢帧
        if buffer_state == BufferState.READY:
            logger.debug(
                f"缓冲区 {write_index} 仍为 READY 状态，读取端处理不及时，丢帧"
            )
            self.frames_dropped += 1
            self.shm_handler.write_index += 1  # 仍需推进 writeIndex
            return False
        elif buffer_state not in (BufferState.EMPTY, BufferState.PROCESSED):
            logger.warning(
                f"缓冲区 {write_index} 状态异常: {buffer_state}({BufferState(buffer_state).name})"
            )
            self.frames_dropped += 1
            return False

        try:
            buffer = self.shm_handler.buffers[write_index]
            # 1. 先标记为写入状态，阻止读取端访问
            buffer.state = BufferState.WRITING

            # 2. 填充所有元数据（完整写入）
            if timestamp is None:
                timestamp = int(time.time() * 1000)

            buffer.metadata.timestamp = timestamp
            buffer.metadata.frame_id = self.frame_counter
            buffer.metadata.width = width
            buffer.metadata.height = height
            buffer.metadata.image_size = len(image_data)
            buffer.metadata.mask_size = len(encoded_mask_data)

            # 3. 填充mask数据大小字段
            buffer.mask_data_size = len(encoded_mask_data)

            # 4. 拷贝所有数据（确保完整性）
            ctypes.memmove(buffer.image_data, image_data, len(image_data))
            ctypes.memmove(buffer.mask_data, encoded_mask_data, len(encoded_mask_data))

            # 5. 数据完全写入后，原子操作设置为就绪状态
            # 这是关键：只有所有数据都写完才设置READY
            buffer.state = BufferState.READY

            # 更新统计信息和写入索引
            self.frames_produced += 1
            self.shm_handler.frames_produced += 1
            self.shm_handler.write_index += 1  # 写入成功后才更新索引
            self.frame_counter += 1

            logger.debug(f"成功写入帧 {self.frame_counter - 1} 到缓冲区 {write_index}")
            return True

        except Exception as e:
            logger.error(f"写入帧数据异常: {e}")
            # 重置缓冲区状态
            if write_index is not None:
                self.shm_handler.buffers[write_index].state = BufferState.EMPTY
            return False

    def _build_mask_data(
        self, masks_info: list, mask_width: int, mask_height: int
    ) -> Optional[bytes]:
        """构建完整的mask数据结构

        Args:
            masks_info: mask信息列表
            mask_width: 整体mask宽度
            mask_height: 整体mask高度

        Returns:
            bytes: 构建好的mask数据，失败返回None
        """
        try:
            if not masks_info:
                return b""  # 空mask数据

            # 1. 构建头部
            header = MaskHeader()
            header.mask_count = len(masks_info)
            header.mask_width = mask_width
            header.mask_height = mask_height
            header.reserved = 0

            # 2. 构建mask信息数组和收集RLE数据
            mask_infos = []
            all_rle_data = b""

            for mask_info in masks_info:
                # 验证必要字段
                required_fields = [
                    "object_id",
                    "rle_data",
                    "original_width",
                    "original_height",
                    "tissue_id",
                ]
                for field in required_fields:
                    if field not in mask_info:
                        logger.error(f"Mask信息缺少必要字段: {field}")
                        return None

                rle_data = mask_info["rle_data"]
                if not isinstance(rle_data, bytes):
                    logger.error("RLE数据必须是bytes类型")
                    return None

                # 创建mask信息结构
                info = MaskInfo()
                info.object_id = mask_info["object_id"]
                info.rle_offset = len(all_rle_data)
                info.rle_counts_length = len(rle_data)  # COCO RLE字节长度
                info.original_width = mask_info["original_width"]
                info.original_height = mask_info["original_height"]
                info.tissue_id = mask_info["tissue_id"]

                mask_infos.append(info)
                all_rle_data += rle_data

            # 3. 组装最终数据
            result = bytes(header)

            # 添加mask信息数组
            for info in mask_infos:
                result += bytes(info)

            # 添加RLE数据区域
            result += all_rle_data

            logger.debug(
                f"构建mask数据成功，总大小: {len(result)} bytes，包含 {len(masks_info)} 个mask"
            )
            return result

        except Exception as e:
            logger.error(f"构建mask数据失败: {e}")
            return None

    def get_stats(self) -> dict:
        """获取统计信息"""
        if not self.shm_handler:
            return {}

        return {
            "frames_produced": self.frames_produced,
            "frames_dropped": self.frames_dropped,
            "shm_frames_produced": self.shm_handler.frames_produced,
            "shm_frames_consumed": self.shm_handler.frames_consumed,
            "shm_frames_dropped": self.shm_handler.frames_dropped,
            "write_index": self.shm_handler.write_index,
            "read_index": self.shm_handler.read_index,
        }

    def cleanup(self):
        """清理资源"""
        self.running = False

        # 先清除handler引用
        self.shm_handler = None

        if self.mmap_obj:
            try:
                self.mmap_obj.close()
            except Exception:
                pass
            self.mmap_obj = None

        if self.shm_fd is not None:
            import os

            try:
                os.close(self.shm_fd)
            except Exception:
                pass
            self.shm_fd = None

        logger.info("MaskIpc资源清理完成")

    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self.running and self.shm_handler is not None

    def __enter__(self):
        """支持上下文管理器"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.cleanup()


if __name__ == "__main__":
    # 简单测试
    def test_mask_ipc():
        """测试MaskIpc基本功能"""
        shm_name = "/mask_algorithm_data"

        with MaskIpc(shm_name) as mask_ipc:
            if not mask_ipc.initialize():
                logger.error("初始化失败")
                return

            # 模拟图像数据 (RGBA格式)
            width, height = 640, 480
            image_data = b"\xff\x00\x00\xff" * (width * height)  # 红色图像

            # 模拟已编码的mask数据（算法进程提供）
            encoded_mask_data = b"\x01\x02\x03\x04" * 100  # 示例编码数据

            # 写入帧数据
            success = mask_ipc.write_frame(image_data, width, height, encoded_mask_data)

            if success:
                logger.info("成功写入测试帧")
                stats = mask_ipc.get_stats()
                logger.info(f"统计信息: {stats}")
            else:
                logger.error("写入测试帧失败")

    test_mask_ipc()
