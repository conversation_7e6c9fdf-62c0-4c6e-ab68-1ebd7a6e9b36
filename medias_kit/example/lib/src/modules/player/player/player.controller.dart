import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/foundation.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';

/// 所属模块: player
///
/// 播放器
class PlayerController extends AppController with StateMixin<ApiModel> {
  PlayerController(super.key, super.routerState);

  /// Create a [Player] to control playback.
  late final player = Player();

  /// Create a [VideoController] to handle video output from [Player].
  late final controller = VideoController(
    player,
    // configuration: const VideoControllerConfiguration(
    //   enableHardwareAcceleration: false,
    // ),
  );

  late final render = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );

  late final recorder = Recorder(
    spaceName: "19709",
    videoCaptureDevice: HostDevice.share.videoSources.value
        .firstWhere((device) => !device.isUsbExtend),
    videoBitrate: 24000,
    width: 3840,
    height: 2160,
    framerate: 60,
  );

  late final monitor = Monitor(
    videoCaptureDevice: HostDevice.share.videoSources.value
        .firstWhere((device) => !device.isUsbExtend),
    width: 1920,
    height: 1080,
    framerate: 30,
  );

  @override
  void onInit() {
    controller.addRender(
      render: render.address,
      renderFunction: ffi.addresses.textureRendererRenderRgbaFrame.address,
    );
    player.setPlaylistMode(PlaylistMode.loop);
    player.setRate(3);
    player.open(
      // Media('/home/<USER>/Desktop/xinanyike.mp4'),
      // Media('/home/<USER>/Desktop/test/output.m3u8'),
      Media('/home/<USER>/Desktop/ttt.mp4'),
    );
    player.stream.width.listen((width) {
      debugPrint("width: $width");
    });
    player.stream.height.listen((height) {
      debugPrint("height: $height");
    });
    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {
    monitor.init();
    recorder.init();
    recorder.recordAudio(true);
  }

  @override
  void onClose() {
    recorder.dispose();
    player.dispose();
    controller.removeRender(render: render.address);

    Future.delayed(const Duration(seconds: 1), () {
      ffi.textureRendererDestroy(render);
    });
  }
}
