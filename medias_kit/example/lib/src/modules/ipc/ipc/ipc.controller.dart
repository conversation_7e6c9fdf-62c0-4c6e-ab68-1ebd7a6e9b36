import 'dart:async';

import 'package:app_foundation/app_foundation.dart';
import 'package:medias_kit/core/medias_kit_ffi.dart' as ffi;
import 'package:medias_kit/medias_kit.dart';

/// 所属模块: ipc
///
/// IPC测试
class IpcController extends AppController with StateMixin<ApiModel> {
  IpcController(super.key, super.routerState);

  MaskIpc? _maskIpc;

  final monitor = Monitor(
    videoCaptureDevice: HostDevice.share.videoSources.value
        .firstWhere((device) => !device.isUsbExtend),
    width: 1920,
    height: 1080,
    framerate: 30,
  );

  late final _maskRenderRef = ffi.textureRendererCreate(
    MediasKit.pluginId.toNativeCharPointer(this),
  );

  Timer? _statusTimer;

  final stats = "".notifier;

  @override
  void onInit() {
    _initializeMaskIpc();
    update(LoadState.success(ApiModel()));
  }

  void _initializeMaskIpc() {
    try {
      // 创建MaskIpc实例，提供颜色数组
      _maskIpc = MaskIpc('/mask_algorithm_data', [
        0xFFFF00FF, // 红色
        0xFF00FFFF, // 绿色
        0xFF0000FF, // 蓝色
        0xFFFFFFFF, // 黄色
        0xFFFF00FF, // 紫色
      ]);

      _maskIpc!.addRenderer('main', _maskRenderRef);

      // 设置连接状态变化回调
      _maskIpc!.onConnectionChanged = (connected) {
        app.logI('MaskIpc连接状态: ${connected ? "已连接" : "已断开"}');
      };

      // 初始化IPC连接
      if (_maskIpc!.initialize()) {
        app.logI('MaskIpc初始化成功');
        _maskIpc!.start();
        app.logI('MaskIpc已启动数据轮询');
      } else {
        app.logE('MaskIpc初始化失败');
      }
    } catch (e) {
      app.logE('MaskIpc初始化异常: $e');
    }
  }

  @override
  void onReady() {
    // 定期打印状态信息
    _statusTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_maskIpc != null) {
        stats.value = _maskIpc!.toString();
      }
    });
    monitor.init();
  }

  @override
  void onClose() {
    _statusTimer?.cancel();
    _maskIpc?.dispose();
    monitor.dispose();
    Future.delayed(const Duration(seconds: 1), () {
      ffi.textureRendererDestroy(_maskRenderRef);
    });
    _maskIpc = null;
    app.logI('IPC控制器已清理');
  }

  /// 获取texture ID用于UI显示
  int? get maskTextureId => _maskIpc?.getTextureId('main');

  /// 获取连接状态
  bool get isConnected => _maskIpc?.isConnected ?? false;

  /// 手动重连
  void reconnect() {
    app.logI('手动重连MaskIpc...');
    _maskIpc?.dispose();
    _initializeMaskIpc();
  }

  /// 切换运行状态
  void toggleRunning() {
    if (_maskIpc?.isRunning == true) {
      _maskIpc?.stop();
      app.logI('MaskIpc已停止');
    } else {
      _maskIpc?.start();
      app.logI('MaskIpc已启动');
    }
  }
}
