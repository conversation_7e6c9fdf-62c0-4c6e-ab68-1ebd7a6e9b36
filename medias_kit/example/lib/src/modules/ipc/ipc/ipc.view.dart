import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:medias_kit/monitor/monitor_view.dart';

import 'ipc.controller.dart';

/// 所属模块: ipc
///
/// IPC测试
class IpcView extends AppView<IpcController> {
  const IpcView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      appBar: AppBar(
        title: const Text('IpcView'),
      ),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 连接状态显示
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'MaskIpc连接状态',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        controller.isConnected
                            ? Icons.check_circle
                            : Icons.error,
                        color:
                            controller.isConnected ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(controller.isConnected ? '已连接' : '未连接'),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 统计信息
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '统计信息',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  ValueListenableBuilder(
                    valueListenable: controller.stats,
                    builder: (context, value, child) {
                      return Text(
                        value,
                        style: Theme.of(context).textTheme.bodySmall,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 操作按钮
          Row(
            children: [
              ElevatedButton(
                onPressed: controller.reconnect,
                child: const Text('重连'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: controller.toggleRunning,
                child: Text(controller.isConnected ? '停止' : '启动'),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Text(
            'Texture预览 (ID: ${controller.maskTextureId})',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: Texture(textureId: controller.maskTextureId!),
                  ),
                ),
                Expanded(
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: MonitorView(monitor: controller.monitor),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
