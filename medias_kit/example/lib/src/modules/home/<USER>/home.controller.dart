import 'dart:convert';
import 'dart:ui';

import 'package:app_foundation/app_foundation.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/foundation.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:medias_kit_example/src/routes/go_paths.dart';
import 'package:screenshot/screenshot.dart';

/// 所属模块: home
///
/// 首页
class HomeController extends AppController
    with StateMixin<ApiModel>, VoiceHandler {
  HomeController(super.key, super.routerState);

  String platformVersion = HostDevice.share.getPlatform();
  final units = ['PB', 'TB', 'GB', 'MB', 'KB', 'B'];

  @override
  void onInit() {
    app.logD(platformVersion);
    final spaceInfo = HostDevice.share.getSpaceInfo();
    app.logD(
      "SpaceInfo | capacity: ${spaceInfo.capacity.bitIndent(bitStep: 10, units: units)}, free: ${spaceInfo.free.bitIndent(bitStep: 10, units: units)}, available: ${spaceInfo.available.bitIndent(bitStep: 10, units: units)}",
    );
    // app.logE(routerState?.extra);
    // HostDevice.share.videoSources.addListener(() {
    //   for (var e in HostDevice.share.videoSources.value) {
    //     debugPrint("${e.name.dartString}      ${e.path.dartString}");
    //   }
    // });
    HostDevice.share.audioSources.addListener(() {
      debugPrint(">>>>>>>>>>>>>>>>>>>");
      for (var e in HostDevice.share.audioSources.value) {
        debugPrint(
          "audioSource: ${e.description.dartString} ===> ${e.volume}",
        );
        // HostDevice.share.setAudioSourceVolume(e, 100);
      }
    });
    HostDevice.share.audioSinks.addListener(() {
      debugPrint(">>>>>>>>>>>>>>>>>>>");
      for (var e in HostDevice.share.audioSinks.value) {
        debugPrint(
          "audioSink: ${e.description.dartString} ===> ${e.volume}",
        );
        // HostDevice.share.setAudioSinkVolume(e, 100);
      }
    });

    HostDevice.share.startListenVideoCaptureDevices();
    HostDevice.share.startListenAudioDevices();

    HostDevice.share.setOnline(true);
    final ret = VoiceHelper.share.prepare();
    debugPrint("********>>>>>>>>>>>>>>>> $ret");
    VoiceHelper.share.addHandler(this);
    VoiceHelper.share.config(
      onAwakened: (aa) {
        debugPrint(">>>>>>>>>>> $aa");
      },
      onRecognizing: (aa, bb) {
        debugPrint(">>>>>>>>>>> ${MediasKit.pluginId}  $aa $bb");
      },
      onError: (aa, bb) {
        debugPrint(">>>>>>>>>>> $aa");
      },
    );

    update(LoadState.success(ApiModel()));
  }

  @override
  void onReady() {
    // if (routerState?.extra == null) {
    //   Future.delayed(const Duration(seconds: 3), () {
    //     multiWindow();
    //   });
    // }
  }

  @override
  void onClose() {}

  void test() {
    // HostDevice.share.startListenVideoCaptureDevices();
    // HostDevice.share.videoSources.addListener(() {
    // app.logD(
    //       ">>>>>>>>>>>>>> ${HostDevice.share.videoSources.value.toString()}");
    // });
    Helper.share.copyFile(
      src: "/home/<USER>/Desktop/xinanyike.mp4",
      // dst: "/media/withai/videos/test.mp4",
      // dst: "/media/withai/ESD-ISO/test.mp4",
      dst: "/media/withai/writable/test.mp4",
      callback: (info) {
        debugPrint(
          "${info.state} ================== Progress: ${info.progress.toPrecision(3)}",
        );
      },
    );

    // Future.delayed(const Duration(seconds: 10), () {
    //   Helper.share.cancelTask("/media/withai/videos/test.mp4");
    // });
  }

  void playVideo() {
    app.openInner(GoPaths.player);
  }

  WindowController? window;

  void multiWindow() async {
    final window = await DesktopMultiWindow.createWindow(
      jsonEncode({
        'args1': 'Sub window',
        'args2': 100,
        'args3': true,
        'business': 'business_test',
      }),
    );
    this.window = window;

    window
      ..setFrame(const Offset(0, 0) & const Size(1280, 720))
      ..center()
      ..setTitle('Another window')
      ..show();
  }

  @override
  VoiceTask? hitTest(String command) {
    return VoiceTask((command) async {
      if (command.contains("再见") || command.contains("拜拜")) {
        return const VoiceTaskResult(
          state: VoiceTaskState.done,
          remindWords: "再见",
        );
      }
      if (command.contains("邀请")) {
        return const VoiceTaskResult(
          state: VoiceTaskState.next,
          remindWords: "为您找到5位神医 请问要邀请第几位",
        );
      }
      return const VoiceTaskResult(remindWords: "不支持的命令");
    }).nextTask((command) async {
      if (command.contains("1")) {
        return const VoiceTaskResult(
          state: VoiceTaskState.done,
          remindWords: "已为您邀请第1位神医",
        );
      }
      return const VoiceTaskResult();
    });
  }
}
