import 'package:app_foundation/app_foundation.dart';
import 'package:flutter/material.dart';
import 'package:medias_kit/medias_kit.dart';
import 'package:medias_kit_example/src/routes/go_paths.dart';

import 'home.controller.dart';

/// 所属模块: home
///
/// 首页
class HomeView extends AppView<HomeController> {
  const HomeView({required super.key, required super.binding});

  @override
  Widget build(BuildContext context) {
    // 注: 如果 AppBar 与数据有关联, 则把 controller.build() 提升为顶级节点
    return Scaffold(
      appBar: AppBar(
        title: const Text('HomeView'),
      ),
      body: controller.build(
        onSuccess: onSuccess,
        onFailure: onFailure,
        onLoading: onLoading,
        onEmpty: onEmpty,
      ),
    );
  }

  Widget onSuccess(BuildContext context, ApiModel value) {
    return Center(
      child: ValueListenableBuilder(
        valueListenable: HostDevice.share.videoSources,
        builder: (context, videoSources, child) => Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            TextButton(
              onPressed: () {
                Peripheral.instance.start();
              },
              child: const Text("开启广播"),
            ),
            TextButton(
              onPressed: () {
                Peripheral.instance.stop();
              },
              child: const Text("关闭连接"),
            ),
            TextButton(
              onPressed: () {
                Peripheral.instance.notify(msgType: 1, data: "test");
              },
              child: const Text("发送通知"),
            ),
            TextButton(
              onPressed: () {
                app.openInner(GoPaths.ipc);
              },
              child: const Text("IPC测试"),
            ),
            Text('Running on: ${controller.platformVersion}\n'),
            const Text('采集设备:\n'),
            for (var item in videoSources)
              TextButton(
                onPressed: () {
                  app.openInner(GoPaths.next, arguments: {
                    "item": item,
                    "windowId": controller.window?.windowId
                  });
                },
                child: Text(item.name.dartString),
              ),
            TextButton(
              onPressed: () {
                app.openInner(GoPaths.multi);
              },
              child: const Text("多路采集"),
            ),
            TextButton(
              onPressed: () {
                app.openInner(GoPaths.test);
              },
              child: const Text("音频设备测试"),
            ),
            TextButton(
              onPressed: controller.multiWindow,
              child: const Text("多窗口测试"),
            ),
            TextButton(
              onPressed: controller.test,
              child: const Text("测试测试"),
            ),
            TextButton(
              onPressed: controller.playVideo,
              child: const Text("播放器播放"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.off);
              },
              child: const Text("关闭LED"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.blue);
              },
              child: const Text("蓝灯常亮"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.blueFlashing);
              },
              child: const Text("蓝灯呼吸"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.red);
              },
              child: const Text("红灯常亮"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.redFlashing);
              },
              child: const Text("红灯呼吸"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.green);
              },
              child: const Text("绿灯常亮"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.greenFlashing);
              },
              child: const Text("绿灯呼吸"),
            ),
            TextButton(
              onPressed: () {
                Light.share.active(LightAction.yellow);
              },
              child: const Text("黄灯常亮"),
            ),
          ],
        ),
      ),
    );
  }

  Widget? onFailure(BuildContext context, String error) {
    return null;
  }

  Widget? onLoading(BuildContext context) {
    return null;
  }

  Widget? onEmpty(BuildContext context) {
    return null;
  }
}
