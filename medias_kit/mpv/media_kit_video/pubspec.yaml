name: media_kit_video
description: 'Native implementation for video playback in package:media_kit.'
publish_to: none
homepage: 'https://github.com/media-kit/media-kit'
repository: 'https://github.com/media-kit/media-kit'
version: 1.3.0
topics:
- video
- video-player
- audio
- audio-player
- cross-platform
environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: '>=3.7.0'
dependencies:
  flutter:
    sdk: flutter
  media_kit:
    path: ../media_kit
  synchronized: ^3.1.0
  wakelock_plus: ^1.1.6
  screen_brightness_android: ^2.0.0
  screen_brightness_platform_interface: ^2.0.0
  volume_controller: ^3.0.2
  universal_platform: ^1.0.0+1
  plugin_platform_interface: ^2.0.2
  web: ^1.1.0
dev_dependencies:
  flutter_lints: ^5.0.0
flutter:
  plugin:
    platforms:
      windows:
        pluginClass: MediaKitVideoPluginCApi
      linux:
        pluginClass: MediaKitVideoPlugin
      macos:
        pluginClass: MediaKitVideoPlugin
      ios:
        pluginClass: MediaKitVideoPlugin
      android:
        package: com.alexmercerind.media_kit_video
        pluginClass: MediaKitVideoPlugin
      web: null
