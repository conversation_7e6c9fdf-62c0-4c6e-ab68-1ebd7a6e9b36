# 算法-App IPC 数据传递系统设计文档

## 1. 系统概述

本系统设计用于 Python3 算法进程与 Flutter Linux App 之间的高性能数据传递，支持 30fps 实时图像和 mask 数据传输。采用共享内存机制实现零拷贝数据传递，通过 pthread 互斥锁和条件变量保证数据同步。算法端使用 Python3+mmap+ctypes 实现，App 端使用 C++实现。

### 1.1 系统架构图

```mermaid
graph TB
    subgraph "Python3算法进程"
        A1[图像采集]
        A2[算法识别分割]
        A3[RLE编码&数据写入]
        A1 --> A2 --> A3
    end

    subgraph "共享内存区域"
        SHM[SharedMemoryHeader<br/>42MB环形缓冲区]
        SYNC[信号量&条件变量<br/>同步机制]
    end

    subgraph "Flutter App进程"
        F1[数据读取]
        F2[Mask解码]
        F3[纹理渲染]
        F4[UI显示]
        F5[存储上传]
        F1 --> F2 --> F3 --> F4
        F2 --> F5
    end

    A3 -.->|1.写入帧数据| SHM
    A3 -.->|2.通知数据就绪| SYNC
    SYNC -.->|3.唤醒App进程| F1
    SHM -.->|4.读取帧数据| F1
    F4 -.->|5.标记处理完成| SYNC
    SYNC -.->|6.等待下一帧| A3

    classDef processBox fill:#e1f5fe,stroke:#0277bd,stroke-width:2px
    classDef memoryBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef dataFlow fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class A1,A2,A3,F1,F2,F3,F4,F5 processBox
    class SHM,SYNC memoryBox
```

## 2. 数据结构设计

### 2.1 核心数据结构

```c
// 帧元数据
typedef struct {
    int64_t timestamp;          // 时间戳（毫秒）
    uint32_t frame_id;          // 帧序号
    uint32_t width;             // 图像宽度
    uint32_t height;            // 图像高度
    uint32_t image_size;        // 图像数据大小
    uint32_t mask_size;         // mask数据大小
    uint8_t reserved[4];        // 字节对齐保留
} FrameMetadata;

// 缓冲区状态
typedef enum {
    BUFFER_EMPTY = 0,      // 空闲状态
    BUFFER_WRITING,        // 算法进程写入中
    BUFFER_READY,          // 数据就绪，待app读取
    BUFFER_READING,        // app读取中
    BUFFER_PROCESSED       // app处理完成，可释放
} BufferState;

// 单个帧缓冲区（固定2MB mask区域）
typedef struct {
    FrameMetadata metadata;     // 帧元数据（8字节对齐）
    BufferState state;          // 缓冲区状态
    uint32_t mask_data_size;    // 实际使用的mask数据大小
    uint8_t image_data[8294400]; // 1920x1080x4 RGBA图像数据
    uint8_t mask_data[2097152]; // 2MB固定mask数据区域
    pthread_mutex_t mutex;      // 缓冲区互斥锁（8字节对齐）
} FrameBuffer;

// 共享内存控制块
typedef struct {
    uint32_t magic_number;           // 魔数校验: 0x4D4B5348('MKSH')
    uint32_t version;                // 版本号
    uint32_t buffer_count;           // 缓冲区数量
    uint32_t write_index;            // 写入索引
    uint32_t read_index;             // 读取索引

    // 同步原语
    pthread_mutex_t global_mutex;    // 全局互斥锁
    pthread_cond_t data_ready_cond;  // 数据就绪条件变量
    pthread_cond_t buffer_free_cond; // 缓冲区释放条件变量

    // 统计信息
    uint64_t frames_produced;        // 生产帧数
    uint64_t frames_consumed;        // 消费帧数
    uint64_t frames_dropped;         // 丢弃帧数

    // 进程状态
    pid_t algorithm_pid;             // 算法进程PID
    pid_t app_pid;                   // App进程PID

    FrameBuffer buffers[4];          // 环形缓冲区数组
} SharedMemoryHeader;
```

### 2.2 Mask 数据格式（兼容 SAM2 标准）

```c
// Mask数据头（内存传递使用无损格式，兼容SAM2标准）
typedef struct {
    uint32_t mask_count;        // mask数量
    uint32_t mask_width;        // mask宽度
    uint32_t mask_height;       // mask高度
    uint8_t reserved[4];        // 字节对齐保留
} MaskHeader;

// 单个mask信息（RLE压缩数据传递）
typedef struct {
    uint32_t object_id;           // 对象ID
    uint32_t rle_offset;        // RLE数据偏移量（相对于RLE数据区域开始）
    uint32_t rle_counts_length; // RLE counts数组长度
    uint32_t original_width;    // 原始mask宽度
    uint32_t original_height;   // 原始mask高度 
    uint32_t tissue_id;        // 组织ID
    uint8_t reserved[4];        // 字节对齐保留
} MaskInfo;

// 标准RLE格式（用于落盘存储，与SAM2/pycocotools兼容）
typedef struct {
    uint32_t size[2];           // [height, width]
    uint32_t counts_length;     // counts数组长度
    uint32_t counts[];          // 未压缩RLE数据：[0段长度, 1段长度, 0段长度, 1段长度, ...]
} UncompressedRle;

// COCO压缩RLE格式（最终存储格式）
typedef struct {
    uint32_t size[2];           // [height, width]
    uint32_t counts_length;     // 压缩字符串长度
    char counts[];              // 压缩RLE字符串（Base64编码）
} CompressedRle;

// 标准帧存储格式（与SAM2官方数据集保持一致）
typedef struct {
    int64_t timestamp;              // 时间戳（毫秒）（8字节对齐）
    uint32_t frame_id;              // 帧ID
    uint32_t width;                 // 图像宽度
    uint32_t height;                // 图像高度
    uint32_t mask_count;            // 该帧的mask数量
    CompressedRle masks[];      // mask RLE数据数组
} FrameData;

// JSON格式存储（与SAM2数据集格式一致）
/*
{
    "frame_id": 12345,
    "timestamp": 1640995200000,
    "width": 1920,
    "height": 1080,
    "masklet": [
        {
            "size": [1080, 1920],
            "counts": "compressed_rle_string_1"
        },
        {
            "size": [1080, 1920],
            "counts": "compressed_rle_string_2"
        }
    ]
}
*/
```

### 2.3 RLE 算法实现（C++版本，兼容 SAM2）

```cpp
// linux/source/algorithm_ipc/rle_codec.hpp
namespace MK {

class RleCodec {
public:
    // 将二进制mask编码为未压缩RLE格式（兼容SAM2）
    static UncompressedRle* encodeMaskToRle(
        const uint8_t* mask_data,
        uint32_t width,
        uint32_t height
    );

    // 将未压缩RLE解码为二进制mask（兼容SAM2）
    static uint8_t* decodeRleToMask(
        const UncompressedRle* rle,
        uint32_t* out_width,
        uint32_t* out_height
    );

    // 压缩RLE数据（使用SAM2标准兼容算法）
    static CompressedRle* compressRle(const UncompressedRle* uncompressed_rle);

    // 解压缩RLE数据
    static UncompressedRle* decompressRle(const CompressedRle* compressed_rle);

    // 保存整帧数据为标准格式（JSON，兼容SAM2）
    static bool saveFrameToJson(
        uint32_t frame_id,
        uint32_t width,
        uint32_t height,
        int64_t timestamp_ms,
        const std::vector<CompressedRle*>& masks,
        const std::string& file_path
    );

    // 从标准JSON格式加载帧数据（兼容SAM2）
    static FrameData* loadFrameFromJson(
        const std::string& file_path
    );

    // 单个mask保存（兼容性接口）
    static bool saveSingleMaskToJson(
        uint32_t object_id,
        uint32_t width,
        uint32_t height,
        int64_t timestamp_ms,
        const CompressedRle* mask_rle,
        const std::string& file_path
    );

private:
    // 标准RLE编码实现（基于pycocotools算法，兼容SAM2）
    static void rleEncode(
        const uint8_t* mask,
        uint32_t width,
        uint32_t height,
        std::vector<uint32_t>& counts
    );

    // 标准RLE解码实现（兼容SAM2）
    static void rleDecode(
        const std::vector<uint32_t>& counts,
        uint32_t width,
        uint32_t height,
        uint8_t* mask
    );

    // 压缩字符串编码（类似LEB128，使用6位/字符和ASCII 48-111，兼容SAM2）
    static std::string rleToString(const std::vector<uint32_t>& counts);

    // 压缩字符串解码（兼容SAM2）
    static std::vector<uint32_t> stringToRle(const std::string& rle_string);
};

}
```

### 2.4 RLE 算法实现示例（兼容 SAM2）

基于源码的完整 C++实现（兼容 SAM2）：

```cpp
// linux/source/algorithm_ipc/rle_codec.cpp
namespace MK {

void RleCodec::rleEncode(
    const uint8_t* mask,
    uint32_t width,
    uint32_t height,
    std::vector<uint32_t>& counts
) {
    counts.clear();

    const uint32_t total_pixels = width * height;
    uint32_t current_count = 0;
    uint8_t current_value = 0;  // 从0开始（背景）

    // 按列优先顺序处理（Fortran order，与SAM2标准保持一致）
    for (uint32_t col = 0; col < width; ++col) {
        for (uint32_t row = 0; row < height; ++row) {
            uint32_t idx = row * width + col;  // 转换为行优先索引
            uint8_t pixel_value = mask[idx] > 0 ? 1 : 0;

            if (pixel_value == current_value) {
                current_count++;
            } else {
                counts.push_back(current_count);
                current_count = 1;
                current_value = pixel_value;
            }
        }
    }
    counts.push_back(current_count);

    // 如果第一个像素是前景，需要在开头添加0
    if (mask[0] > 0) {
        counts.insert(counts.begin(), 0);
    }
}

void RleCodec::rleDecode(
    const std::vector<uint32_t>& counts,
    uint32_t width,
    uint32_t height,
    uint8_t* mask
) {
    memset(mask, 0, width * height);

    uint32_t pixel_idx = 0;
    bool is_foreground = false;

    // 按列优先顺序填充（Fortran order）
    for (size_t i = 0; i < counts.size(); ++i) {
        uint32_t run_length = counts[i];

        for (uint32_t j = 0; j < run_length; ++j) {
            if (pixel_idx >= width * height) break;

            // 将列优先索引转换为行优先索引
            uint32_t col = pixel_idx / height;
            uint32_t row = pixel_idx % height;
            uint32_t actual_idx = row * width + col;

            mask[actual_idx] = is_foreground ? 1 : 0;
            pixel_idx++;
        }

        is_foreground = !is_foreground;
    }
}

std::string RleCodec::rleToString(const std::vector<uint32_t>& counts) {
    std::string result;
    result.reserve(counts.size() * 2);  // 预分配空间

    for (size_t i = 0; i < counts.size(); ++i) {
        uint32_t x = counts[i];

        // 对于索引大于2的元素，计算差值（SAM2标准压缩技巧）
        if (i > 2) {
            x -= counts[i - 2];
        }

        // LEB128风格编码，使用6位/字符和ASCII 48-111
        bool more = true;
        while (more) {
            uint32_t c = x & 0x1f;  // 取低5位
            x >>= 5;

            // 检查是否还有更多位需要编码
            more = (c & 0x10) ? (x != static_cast<uint32_t>(-1)) : (x != 0);

            if (more) {
                c |= 0x20;  // 设置继续位
            }

            c += 48;  // 转换为ASCII字符(48-111)
            result.push_back(static_cast<char>(c));
        }
    }

    return result;
}

std::vector<uint32_t> RleCodec::stringToRle(const std::string& rle_string) {
    std::vector<uint32_t> counts;
    counts.reserve(rle_string.length());

    size_t pos = 0;
    uint32_t m = 0;

    while (pos < rle_string.length()) {
        uint32_t x = 0;
        uint32_t k = 0;
        bool more = true;

        while (more) {
            if (pos >= rle_string.length()) break;

            uint32_t c = static_cast<uint8_t>(rle_string[pos]) - 48;
            x |= (c & 0x1f) << (5 * k);
            more = (c & 0x20) != 0;
            pos++;
            k++;

            if (!more && (c & 0x10)) {
                x |= static_cast<uint32_t>(-1) << (5 * k);
            }
        }

        // 对于索引大于2的元素，还原差值
        if (m > 2) {
            x += counts[m - 2];
        }

        counts.push_back(x);
        m++;
    }

    return counts;
}

CompressedRle* RleCodec::compressRle(const UncompressedRle* uncompressed_rle) {
    std::vector<uint32_t> counts(
        uncompressed_rle->counts,
        uncompressed_rle->counts + uncompressed_rle->counts_length
    );

    std::string compressed_string = rleToString(counts);

    size_t total_size = sizeof(CompressedRle) + compressed_string.length() + 1;
    CompressedRle* compressed = static_cast<CompressedRle*>(malloc(total_size));

    compressed->size[0] = uncompressed_rle->size[0];
    compressed->size[1] = uncompressed_rle->size[1];
    compressed->counts_length = compressed_string.length();

    memcpy(compressed->counts, compressed_string.c_str(), compressed_string.length());
    compressed->counts[compressed_string.length()] = '\0';

    return compressed;
}

}
```

### 2.5 内存布局（固定大小设计）

```
共享内存布局 (总大小: 42MB)
┌─────────────────────────────────────────────────────────┐
│ SharedMemoryHeader (1KB)                                │
├─────────────────────────────────────────────────────────┤
│ FrameBuffer[0] (10.5MB)                                 │
│   ├─ state + metadata (64B)                             │
│   ├─ image_data (8MB)                                   │
│   ├─ mask_data_size (4B)                                │
│   ├─ mask_data (2MB固定)                                │
│   └─ mutex (32B)                                        │
├─────────────────────────────────────────────────────────┤
│ FrameBuffer[1] (10.5MB)                                 │
├─────────────────────────────────────────────────────────┤
│ FrameBuffer[2] (10.5MB)                                 │
├─────────────────────────────────────────────────────────┤
│ FrameBuffer[3] (10.5MB)                                 │
└─────────────────────────────────────────────────────────┘
```

### 2.6 Mask 数据区域布局（RLE 压缩格式）

```
mask_data[2097152] 区域布局:
┌─────────────────────────────────────────────────────────┐
│ MaskHeader (16B) (兼容SAM2标准)                  │
│   ├─ mask_count: 当前帧mask对象数量                      │
│   ├─ mask_width: 图像宽度 (1920)                        │
│   ├─ mask_height: 图像高度 (1080)                       │
│   └─ reserved: 保留字段                                 │
├─────────────────────────────────────────────────────────┤
│ MaskInfo数组 (变长，每个28B) (兼容SAM2)           │
│   ├─ mask_info[0]: 第一个mask的RLE信息                  │
│   │   ├─ object_id: mask唯一标识                          │
│   │   ├─ rle_offset: 在RLE数据区的偏移                  │
│   │   ├─ rle_counts_length: counts数组长度              │
│   │   ├─ original_width/height: 原始尺寸                │
│   │   └─ tissue_id: 渲染颜色                           │
│   ├─ mask_info[1]: 第二个mask信息                       │
│   └─ ...                                                │
├─────────────────────────────────────────────────────────┤
│ RLE数据区域 (紧凑存储)                                   │
│   ├─ mask[0] RLE counts: [count0, count1, count2, ...]  │
│   ├─ mask[1] RLE counts: [count0, count1, ...]          │
│   └─ ...                                                │
└─────────────────────────────────────────────────────────┘

数据访问方式:
- mask_info[i].rle_offset 指向该mask的RLE counts数组起始位置
- counts数组以uint32_t格式连续存储
- App侧通过RLE解码生成RGBA纹理用于渲染
```

## 3. 共享内存管理策略

### 3.1 内存创建和管理责任

**重要说明：共享内存由 Flutter 应用端创建和管理**

- **App 端职责**：

  - 在初始化时创建 42MB 共享内存区域
  - 初始化内存布局和同步原语（互斥锁、条件变量）
  - 管理内存的生命周期（创建、清理、销毁）
  - 监控内存使用状态和异常处理

- **算法端职责**：
  - 连接到已存在的共享内存
  - 写入图像和 mask 数据
  - 遵循同步协议进行数据交换
  - 处理连接断开和重连

**内存创建流程**：

```cpp
// App端在algorithmIpcCreate()中执行：
1. shm_open() 创建命名共享内存
2. ftruncate() 设置42MB大小
3. mmap() 映射到进程地址空间
4. 初始化SharedMemoryHeader
5. 初始化所有FrameBuffer的同步原语
6. 设置初始状态为EMPTY
```

**算法端连接流程**：

```cpp
// 算法端在algorithm_shm_connect()中执行：
1. shm_open() 连接到已存在的共享内存
2. mmap() 映射到进程地址空间
3. 验证内存布局和版本兼容性
4. 准备好进行数据写入
```

## 4. App 侧详细设计

### 4.1 IPC 管理器组件设计

#### 4.1.1 核心类结构

```cpp
// linux/source/algorithm_ipc/algorithm_ipc.hpp
namespace MK {

class AlgorithmIpcManager {
public:
    AlgorithmIpcManager();
    ~AlgorithmIpcManager();

    // 初始化和清理（App端创建并管理共享内存）
    bool initialize(const std::string& shm_name);
    void cleanup();

    // 数据接收
    bool receiveFrame(FrameData& frame_data);
    void markFrameProcessed(uint32_t frame_id);

    // 状态查询
    bool isConnected() const;
    uint64_t getFramesReceived() const;
    uint64_t getFramesDropped() const;

private:
    SharedMemoryHeader* shm_header_;
    int shm_fd_;
    std::string shm_name_;
    std::thread monitor_thread_;
    std::atomic<bool> running_;

    void monitorConnection();
    bool waitForData(int timeout_ms);
};

// 帧数据包装类
class FrameData {
public:
    FrameData(const FrameBuffer* buffer);

    // 图像数据访问
    const uint8_t* getImageData() const;
    size_t getImageSize() const;

    // Mask RLE数据访问和解析
    const MaskHeader* getMaskHeader() const;
    const MaskInfo* getMaskInfoArray() const;
    const uint8_t* getRleDataRegion() const;
    std::vector<DecodedMask> decodeMasks() const;

    // 元数据访问
    const FrameMetadata& getMetadata() const;
    uint32_t getFrameId() const;
    int64_t getTimestamp() const;

private:
    const FrameBuffer* buffer_;
    mutable std::vector<DecodedMask> decoded_masks_;
    mutable bool masks_decoded_;
};

// 解码后的mask数据（用于渲染）
struct DecodedMask {
    uint32_t object_id;
    uint32_t width;
    uint32_t height;
    uint32_t tissue_id;
    std::vector<uint8_t> binary_mask;   // 解码的二进制mask (0/1)
    std::vector<uint8_t> rgba_texture;  // 生成的RGBA纹理数据（用于渲染）
};

}
```

#### 4.1.2 FFI 接口设计

```c
// linux/include/priv\u0001\u001Date/algorithm_ipc/algorithm_ipc_ffi.h

#ifdef __cplusplus
extern "C" {
#endif

// IPC管理器引用类型
typedef void* AlgorithmIpcManagerRef;
typedef void* FrameDataRef;

// 初始化IPC管理器（App端创建共享内存）
ATTRIBUTES AlgorithmIpcManagerRef
algorithmIpcCreate(const char* shm_name);

// 销毁IPC管理器
ATTRIBUTES void
algorithmIpcDestroy(AlgorithmIpcManagerRef manager);

// 接收一帧数据 (非阻塞)
ATTRIBUTES FrameDataRef
algorithmIpcReceiveFrame(AlgorithmIpcManagerRef manager, int timeout_ms);

// 标记帧处理完成
ATTRIBUTES void
algorithmIpcMarkFrameProcessed(AlgorithmIpcManagerRef manager, uint32_t frame_id);

// 获取图像数据
ATTRIBUTES const uint8_t*
frameDataGetImageData(FrameDataRef frame_data, uint32_t* size);

// 获取帧元数据
ATTRIBUTES FrameMetadata*
frameDataGetMetadata(FrameDataRef frame_data);

// 获取mask RLE数据信息
ATTRIBUTES const MaskHeader*
frameDataGetMaskHeader(FrameDataRef frame_data);

// 获取解码后的mask数据
ATTRIBUTES int32_t
frameDataGetDecodedMasks(FrameDataRef frame_data, DecodedMask** masks);

// 释放帧数据
ATTRIBUTES void
frameDataDestroy(FrameDataRef frame_data);

#ifdef __cplusplus
}
#endif
```

### 4.2 双层渲染架构设计

#### 4.2.1 架构概述

```
数据流向：
Python3算法 → 共享内存 → C++处理 → 双层渲染
                                │
                                ├─ 图像层: TextureRenderer 渲染
                                └─ Mask层: Flutter Shader 渲染
```

#### 4.2.2 统一帧管理器

```cpp
// linux/source/algorithm_ipc/synchronized_frame_manager.hpp
namespace MK {

class SynchronizedFrameManager {
public:
    struct DualLayerFrame {
        uint32_t frame_id;
        int64_t timestamp;

        // 图像层数据（给TextureRenderer使用）
        std::vector<uint8_t> image_data;  // RGBA 1920x1080x4
        int64_t image_texture_id;         // 渲染后的纹理ID
        bool image_rendered;

        // Mask层数据（给Flutter Shader使用）
        std::vector<uint8_t> mask_color_ids; // R8 1920x1080x1，像素值=颜色ID(1-255)
        std::vector<uint32_t> mask_colors;    // 颜色数组，索引对应ID
        bool mask_ready;

        bool is_complete() const { return image_rendered && mask_ready; }
    };

    SynchronizedFrameManager();
    ~SynchronizedFrameManager();

    bool init();
    void cleanup();

    // 新帧数据到达（从共享内存读取）
    void processNewFrame(const FrameBuffer& buffer);

    // TextureRenderer完成图像渲染后调用
    void onImageRenderComplete(uint32_t frame_id, int64_t texture_id);

    // Flutter获取就绪帧（保证同步）
    std::optional<DualLayerFrame> getReadyFrame();

private:
    AlgorithmIpcManager ipc_manager_;
    std::unique_ptr<RleCodec> rle_codec_;

    // 帧缓存（保留最近3帧）
    std::map<uint32_t, DualLayerFrame> frame_cache_;
    std::atomic<uint32_t> ready_frame_id_{0};
    std::mutex frame_mutex_;

    // 处理mask数据：按顺序排列，后面的优先级更高
    void processMaskData(const FrameBuffer& buffer, DualLayerFrame& frame);

    // 创建mask颜色ID纹理（R8格式）
    void createMaskColorTexture(const std::vector<DecodedMask>& masks,
                               std::vector<uint8_t>& color_ids,
                               std::vector<uint32_t>& colors);

    // 清理过期帧
    void cleanupExpiredFrames(uint32_t current_frame_id);

    // 通知Flutter新帧就绪
    void notifyFlutterFrameReady(uint32_t frame_id);
};

}
```

#### 4.2.3 优先级排序协议要求

**算法侧输出要求**：

1. **Mask 优先级排序**：算法输出的 mask 数组必须按优先级从低到高排序

   - 数组索引 0 = 最低优先级 mask
   - 数组索引 n = 最高优先级 mask
   - 后面的 mask 在重叠区域覆盖前面的 mask

2. **颜色集合构建**：shader 中的颜色数组必须与 mask 数组顺序一致

   - `uMaskColors[0]` = 背景色（透明）
   - `uMaskColors[i+1]` = `masks[i].tissue_id`
   - 颜色数组索引 = mask 纹理像素值 = 优先级顺序

3. **纹理编码规则**：
   - 像素值 0 = 背景（无 mask）
   - 像素值 i+1 = masks[i]对应的颜色 ID
   - 重叠处理：后面的 mask 直接覆盖前面的（高优先级覆盖低优先级）

**重要协议约定**：

- **无 priority 字段**：整个数据交互过程中不包含 priority 字段
- **算法内部排序**：算法进程内部负责将每帧的 masks 按优先级排好序后输出
- **数组顺序即优先级**：App 端接收到的 mask 数组顺序就是最终的渲染优先级
- **简化设计**：避免复杂的优先级计算，确保实时性能

#### 4.2.4 Mask 处理实现

```cpp
// mask数据处理：严格按优先级排序处理
void SynchronizedFrameManager::createMaskColorTexture(
    const std::vector<DecodedMask>& masks,
    std::vector<uint8_t>& color_ids,
    std::vector<uint32_t>& colors) {

    // 算法侧已按优先级排序输出，数组顺序即为渲染优先级

    color_ids.assign(1920 * 1080, 0); // 初始化为0（背景）
    colors.clear();
    colors.push_back(0x00000000); // colors[0] = 透明背景

    // 按算法提供的优先级顺序处理，构建颜色集合
    for (size_t i = 0; i < masks.size() && i < 255; i++) {
        const auto& mask = masks[i];
        uint8_t color_id = i + 1; // ID从1开始，对应优先级顺序

        // 按优先级顺序保存颜色到集合
        colors.push_back(mask.tissue_id);

        // 将mask像素填充为对应的优先级颜色ID
        for (uint32_t y = 0; y < 1080; y++) {
            for (uint32_t x = 0; x < 1920; x++) {
                if (mask.getMaskValue(x, y)) {
                    // 高优先级mask覆盖低优先级（i越大优先级越高）
                    color_ids[y * 1920 + x] = color_id;
                }
            }
        }
    }
}
```

#### 4.2.4 Flutter Shader 渲染

```glsl
// assets/shaders/medical_mask_overlay.frag
#version 320 es
precision highp float;

uniform sampler2D uImageTexture;    // 图像层纹理（来自TextureRenderer）
uniform sampler2D uMaskTexture;     // Mask颜色ID纹理（R8格式）
uniform vec4 uMaskColors[256];      // 颜色数组，索引=颜色ID
uniform int uMaskCount;             // 有效mask数量
uniform float uOverallOpacity;      // 整体透明度

in vec2 vTexCoord;
out vec4 fragColor;

void main() {
    // 读取图像层颜色
    vec4 imageColor = texture(uImageTexture, vTexCoord);

    // 读取mask颜色ID
    float maskId = texture(uMaskTexture, vTexCoord).r * 255.0;

    if (maskId < 1.0) {
        // 背景区域，直接显示图像
        fragColor = imageColor;
        return;
    }

    int colorIndex = int(maskId);
    if (colorIndex >= uMaskCount) {
        fragColor = imageColor;
        return;
    }

    // 获取mask颜色
    vec4 maskColor = uMaskColors[colorIndex];

    // 医疗级屏幕混合（产生发光效果）
    vec3 screenBlended = 1.0 - (1.0 - imageColor.rgb) * (1.0 - maskColor.rgb);

    // 最终混合
    float finalAlpha = maskColor.a * uOverallOpacity;
    vec3 finalColor = mix(imageColor.rgb, screenBlended, finalAlpha);

    fragColor = vec4(finalColor, imageColor.a);
}
```

### 4.3 数据管理层设计

#### 4.3.1 帧缓存管理器

```cpp
// linux/source/algorithm_ipc/frame_cache_manager.hpp
namespace MK {

class FrameCacheManager {
public:
    FrameCacheManager(size_t max_cache_size = 100);
    ~FrameCacheManager();

    // 缓存管理
    void cacheFrame(uint32_t frame_id, const FrameData& frame_data);
    std::optional<CachedFrame> getFrame(uint32_t frame_id);
    void removeFrame(uint32_t frame_id);
    void clearCache();

    // 存储导出（使用RLE压缩，兼容SAM2）
    bool exportFrame(uint32_t frame_id, const std::string& export_path);
    bool exportMaskOnly(uint32_t frame_id, uint32_t object_id, const std::string& export_path);
    std::vector<uint32_t> getPendingUploads();

    // 标准格式支持（JSON格式，一帧多mask，兼容SAM2）
    bool exportFrameAsJson(uint32_t frame_id, const std::string& export_path);
    bool importFrameFromJson(const std::string& import_path, uint32_t& frame_id);

private:
    struct CachedFrame {
        uint32_t frame_id;
        int64_t timestamp;
        std::vector<uint8_t> image_data;
        std::vector<uint8_t> mask_data;
        FrameMetadata metadata;
    };

    std::unordered_map<uint32_t, CachedFrame> cache_;
    std::mutex cache_mutex_;
    size_t max_cache_size_;

    void evictOldFrames();
};

}
```

### 4.4 Dart 层 API 设计

#### 4.4.1 FFI 绑定生成

首先需要在 `ffigen.yaml` 中添加算法 IPC 的 FFI 绑定配置：

```yaml
# ffigen.yaml (部分配置)
headers:
  entry-points:
    - "linux/include/private/algorithm_ipc/algorithm_ipc_ffi.h"
  include-directives:
    - "linux/include/private/algorithm_ipc/algorithm_ipc_ffi.h"

functions:
  include:
    - "algorithmIpc.*"
    - "frameData.*"

structs:
  include:
    - "DecodedMask"
    - "FrameMetadata"
```

#### 4.4.2 AlgorithmIpc 类

```dart
// lib/algorithm_ipc/algorithm_ipc.dart
import 'dart:async';
import 'dart:ffi' as ffi;
import 'dart:typed_data';
import 'package:ffi/ffi.dart';

import '../generated_bindings.dart' as bindings;

class AlgorithmIpc {
  static final bindings.MediasKitBindings _bindings = bindings.MediasKitBindings(
    ffi.DynamicLibrary.open('libmedias_kit_plugin.so')
  );

  ffi.Pointer<ffi.Void>? _ipcManagerRef;
  StreamController<AlgorithmFrame>? _frameController;
  Timer? _receiveTimer;

  // 初始化IPC连接（App端负责创建共享内存）
  bool initialize(String shmName) {
    try {
      final shmNamePtr = shmName.toNativeUtf8();
      _ipcManagerRef = _bindings.algorithmIpcCreate(shmNamePtr.cast());
      calloc.free(shmNamePtr);

      if (_ipcManagerRef != ffi.nullptr) {
        _startFrameReceiving();
        return true;
      }
      return false;
    } catch (e) {
      print('AlgorithmIpc initialization failed: $e');
      return false;
    }
  }

  // 帧数据流
  Stream<AlgorithmFrame> get frameStream =>
    _frameController?.stream ?? const Stream.empty();

  // 标记帧处理完成
  void markFrameProcessed(int frameId) {
    if (_ipcManagerRef == null) return;

    _bindings.algorithmIpcMarkFrameProcessed(
      _ipcManagerRef!,
      frameId
    );
  }

  void _startFrameReceiving() {
    _frameController = StreamController<AlgorithmFrame>.broadcast();

    _receiveTimer = Timer.periodic(const Duration(milliseconds: 33), (timer) {
      if (_ipcManagerRef == null) {
        timer.cancel();
        return;
      }

      try {
        final frameDataRef = _bindings.algorithmIpcReceiveFrame(
          _ipcManagerRef!,
          10 // 10ms timeout
        );

        if (frameDataRef != ffi.nullptr) {
          final frame = _parseFrameData(frameDataRef);
          if (frame != null) {
            _frameController?.add(frame);
          }

          // 释放帧数据
          _bindings.frameDataDestroy(frameDataRef);
        }
      } catch (e) {
        print('Frame receiving error: $e');
      }
    });
  }

  AlgorithmFrame? _parseFrameData(ffi.Pointer<ffi.Void> frameDataRef) {
    try {
      // 获取图像数据
      final imageSizePtr = calloc<ffi.Uint32>();
      final imageDataPtr = _bindings.frameDataGetImageData(frameDataRef, imageSizePtr);
      final imageSize = imageSizePtr.value;
      calloc.free(imageSizePtr);

      if (imageDataPtr == ffi.nullptr || imageSize == 0) {
        return null;
      }

      // 获取mask header信息
      final maskHeaderPtr = _bindings.frameDataGetMaskHeader(frameDataRef);
      if (maskHeaderPtr == ffi.nullptr) {
        return null;
      }

      // 获取解码后的mask数据（C++侧已完成RLE解码）
      final masksPtr = calloc<ffi.Pointer<bindings.DecodedMask>>();
      final maskCount = _bindings.frameDataGetDecodedMasks(frameDataRef, masksPtr);

      final masks = <AlgorithmMask>[];
      if (maskCount > 0 && masksPtr.value != ffi.nullptr) {
        for (int i = 0; i < maskCount; i++) {
          final maskPtr = masksPtr.value.elementAt(i);
          final mask = _parseDecodedMask(maskPtr.ref);
          if (mask != null) {
            masks.add(mask);
          }
        }
      }

      calloc.free(masksPtr);

      // 获取帧元数据
      final metadataPtr = _bindings.frameDataGetMetadata(frameDataRef);
      if (metadataPtr == ffi.nullptr) {
        return null;
      }

      final metadata = metadataPtr.ref;

      return AlgorithmFrame(
        frameId: metadata.frame_id,
        timestamp: metadata.timestamp,
        width: metadata.width,
        height: metadata.height,
        masks: masks,
        textureId: 0, // TODO: 需要集成到TextureRenderer
      );
    } catch (e) {
      print('Parse frame data error: $e');
      return null;
    }
  }

  AlgorithmMask? _parseDecodedMask(bindings.DecodedMask maskStruct) {
    try {
      // 获取RGBA纹理数据（C++侧已从RLE解码并生成）
      final rgbaSize = maskStruct.width * maskStruct.height * 4;
      final rgbaData = Uint8List.fromList(
        maskStruct.rgba_texture.cast<ffi.Uint8>().asTypedList(rgbaSize)
      );

      return AlgorithmMask(
        maskId: maskStruct.object_id,
        colorRgba: maskStruct.tissue_id,
        width: maskStruct.width,
        height: maskStruct.height,
        rgbaData: rgbaData,
      );
    } catch (e) {
      print('Parse mask error: $e');
      return null;
    }
  }

  void dispose() {
    _receiveTimer?.cancel();
    _receiveTimer = null;

    if (_ipcManagerRef != null) {
      _bindings.algorithmIpcDestroy(_ipcManagerRef!);
      _ipcManagerRef = null;
    }

    _frameController?.close();
    _frameController = null;
  }
}
```

#### 4.4.3 数据模型类

```dart
// lib/algorithm_ipc/models.dart
class AlgorithmFrame {
  final int frameId;
  final int timestamp;
  final int width;
  final int height;
  final List<AlgorithmMask> masks;  // 每个mask有自己的颜色信息
  final int textureId;

  AlgorithmFrame({
    required this.frameId,
    required this.timestamp,
    required this.width,
    required this.height,
    required this.masks,
    required this.textureId,
  });

  factory AlgorithmFrame.fromMap(Map<String, dynamic> map) {
    return AlgorithmFrame(
      frameId: map['frameId'],
      timestamp: map['timestamp'],
      width: map['width'],
      height: map['height'],
      masks: (map['masks'] as List).map((m) => AlgorithmMask.fromMap(m)).toList(),
      textureId: map['textureId'],
    );
  }
}

class AlgorithmMask {
  final int maskId;
  final int colorRgba;
  final int width;
  final int height;
  final Uint8List rgbaData;  // RLE解码后的RGBA纹理数据

  AlgorithmMask({
    required this.maskId,
    required this.colorRgba,
    required this.width,
    required this.height,
    required this.rgbaData,
  });

  factory AlgorithmMask.fromMap(Map<String, dynamic> map) {
    return AlgorithmMask(
      maskId: map['maskId'],
      colorRgba: map['colorRgba'],
      width: map['width'],
      height: map['height'],
      rgbaData: map['rgbaData'],
    );
  }

  Color get color => Color(colorRgba);
}

enum AlgorithmRenderMode {
  imageOnly,
  maskOnly,
  imageWithMask,
  splitView,
}
```

### 4.5 使用示例

```dart
// example/lib/algorithm_demo.dart
class AlgorithmDemo extends StatefulWidget {
  @override
  _AlgorithmDemoState createState() => _AlgorithmDemoState();
}

class _AlgorithmDemoState extends State<AlgorithmDemo> {
  final AlgorithmIpc _ipc = AlgorithmIpc();
  AlgorithmRenderMode _renderMode = AlgorithmRenderMode.imageWithMask;

  @override
  void initState() {
    super.initState();
    _initializeIpc();
  }

  void _initializeIpc() {
    final success = _ipc.initialize('/medias_kit_algorithm_shm');
    if (success) {
      _ipc.frameStream.listen(_onFrameReceived);
    }
  }

  void _onFrameReceived(AlgorithmFrame frame) {
    // 处理接收到的帧数据
    print('Received frame ${frame.frameId} with ${frame.masks.length} masks');

    // 缓存mask数据用于上传
    if (frame.masks.isNotEmpty) {
      _cacheMaskData(frame);
    }

    // 标记处理完成
    _ipc.markFrameProcessed(frame.frameId);
  }

  void _cacheMaskData(AlgorithmFrame frame) {
    // 实现mask数据缓存和上传逻辑
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: StreamBuilder<AlgorithmFrame>(
              stream: _ipc.frameStream,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }

                return Texture(textureId: snapshot.data!.textureId);
              },
            ),
          ),
          _buildControlPanel(),
        ],
      ),
    );
  }

  Widget _buildControlPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          DropdownButton<AlgorithmRenderMode>(
            value: _renderMode,
            onChanged: (mode) {
              if (mode != null) {
                setState(() => _renderMode = mode);
                _ipc.setRenderMode(mode);
              }
            },
            items: AlgorithmRenderMode.values.map((mode) {
              return DropdownMenuItem(
                value: mode,
                child: Text(mode.toString().split('.').last),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _ipc.dispose();
    super.dispose();
  }
}
```

## 5. 健壮性设计与异常处理

### 5.1 进程崩溃死锁防护机制

**当前设计的潜在问题**：

1. **算法进程崩溃**：可能导致互斥锁永久锁定，App 端无法读取数据
2. **App 进程崩溃**：共享内存泄漏，算法端可能无限等待
3. **条件变量死锁**：进程异常退出时未释放同步原语

**解决方案**：

#### 5.1.1 进程存活检测机制

```c
// 扩展SharedMemoryHeader，添加健壮性字段
typedef struct {
    uint32_t magic_number;           // 魔数校验: 0x4D4B5348
    uint32_t version;                // 版本号
    uint32_t buffer_count;           // 缓冲区数量
    uint32_t write_index;            // 写入索引
    uint32_t read_index;             // 读取索引

    // 健壮性控制
    int64_t algorithm_heartbeat;     // 算法进程心跳时间戳
    int64_t app_heartbeat;           // App进程心跳时间戳
    uint32_t algorithm_pid;          // 算法进程PID
    uint32_t app_pid;                // App进程PID
    uint32_t cleanup_magic;          // 清理标记: 0xDEADBEEF

    // 健壮互斥锁（支持所有者死亡检测）
    pthread_mutex_t global_mutex;    // 使用PTHREAD_MUTEX_ROBUST属性
    pthread_cond_t data_ready_cond;  // 数据就绪条件变量
    pthread_cond_t buffer_free_cond; // 缓冲区释放条件变量

    // 统计信息
    uint64_t frames_produced;        // 生产帧数
    uint64_t frames_consumed;        // 消费帧数
    uint64_t frames_dropped;         // 丢弃帧数
    uint64_t recovery_count;         // 崩溃恢复次数

    FrameBuffer buffers[4];          // 环形缓冲区数组
} SharedMemoryHeader;
```

#### 5.1.2 健壮互斥锁初始化

```c
// App端创建共享内存时的健壮性初始化
int initialize_robust_mutexes(SharedMemoryHeader* header) {
    pthread_mutexattr_t mutex_attr;
    pthread_condattr_t cond_attr;

    // 1. 配置健壮互斥锁属性
    pthread_mutexattr_init(&mutex_attr);
    pthread_mutexattr_setpshared(&mutex_attr, PTHREAD_PROCESS_SHARED);
    pthread_mutexattr_setrobust(&mutex_attr, PTHREAD_MUTEX_ROBUST);  // 关键设置
    pthread_mutexattr_settype(&mutex_attr, PTHREAD_MUTEX_ERRORCHECK);

    // 2. 初始化全局互斥锁
    if (pthread_mutex_init(&header->global_mutex, &mutex_attr) != 0) {
        return -1;
    }

    // 3. 配置条件变量属性
    pthread_condattr_init(&cond_attr);
    pthread_condattr_setpshared(&cond_attr, PTHREAD_PROCESS_SHARED);

    // 4. 初始化条件变量
    pthread_cond_init(&header->data_ready_cond, &cond_attr);
    pthread_cond_init(&header->buffer_free_cond, &cond_attr);

    // 5. 初始化每个缓冲区的互斥锁
    for (int i = 0; i < 4; i++) {
        pthread_mutex_init(&header->buffers[i].mutex, &mutex_attr);
    }

    // 6. 设置初始心跳
    header->algorithm_heartbeat = 0;
    header->app_heartbeat = get_current_time_ms();
    header->cleanup_magic = 0;

    pthread_mutexattr_destroy(&mutex_attr);
    pthread_condattr_destroy(&cond_attr);
    return 0;
}
```

#### 5.1.3 进程崩溃检测与恢复

```c
// 健壮锁处理函数
int robust_mutex_lock(pthread_mutex_t* mutex, const char* operation) {
    int result = pthread_mutex_lock(mutex);

    switch (result) {
        case 0:
            return 0;  // 成功获取锁

        case EOWNERDEAD:
            // 前一个所有者进程崩溃，互斥锁处于不一致状态
            printf("Detected dead owner for %s, attempting recovery...\n", operation);

            // 标记互斥锁状态为一致
            if (pthread_mutex_consistent(mutex) == 0) {
                printf("Successfully recovered mutex for %s\n", operation);
                return 0;
            } else {
                printf("Failed to recover mutex for %s\n", operation);
                pthread_mutex_unlock(mutex);
                return -1;
            }

        case ENOTRECOVERABLE:
            // 互斥锁处于不可恢复状态
            printf("Mutex for %s is not recoverable, system restart required\n", operation);
            return -2;

        default:
            printf("Mutex lock failed for %s: %s\n", operation, strerror(result));
            return -1;
    }
}

// App端健壮性数据读取
int robust_receive_frame(AlgorithmIpcManager* manager, FrameData* frame_data) {
    SharedMemoryHeader* header = manager->shm_header_;

    // 1. 检查算法进程存活状态
    int64_t current_time = get_current_time_ms();
    if (current_time - header->algorithm_heartbeat > 5000) {  // 5秒超时
        printf("Algorithm process appears to be dead, attempting recovery...\n");

        // 检查进程是否真的死亡
        if (header->algorithm_pid > 0 && kill(header->algorithm_pid, 0) != 0) {
            // 进程确实死亡，执行清理
            return handle_algorithm_process_death(manager);
        }
    }

    // 2. 健壮锁获取
    if (robust_mutex_lock(&header->global_mutex, "receive_frame") != 0) {
        return -1;
    }

    // 3. 正常数据读取逻辑
    int result = read_frame_data_internal(header, frame_data);

    // 4. 更新心跳
    header->app_heartbeat = current_time;

    pthread_mutex_unlock(&header->global_mutex);
    return result;
}

// 算法进程死亡处理
int handle_algorithm_process_death(AlgorithmIpcManager* manager) {
    SharedMemoryHeader* header = manager->shm_header_;

    printf("Handling algorithm process death...\n");

    // 1. 重置所有缓冲区状态
    for (int i = 0; i < 4; i++) {
        if (robust_mutex_lock(&header->buffers[i].mutex, "cleanup_buffer") == 0) {
            header->buffers[i].state = BUFFER_EMPTY;
            pthread_mutex_unlock(&header->buffers[i].mutex);
        }
    }

    // 2. 重置共享内存状态
    header->write_index = 0;
    header->read_index = 0;
    header->algorithm_pid = 0;
    header->algorithm_heartbeat = 0;
    header->recovery_count++;

    // 3. 广播条件变量，唤醒可能等待的线程
    pthread_cond_broadcast(&header->data_ready_cond);
    pthread_cond_broadcast(&header->buffer_free_cond);

    printf("Algorithm process death handled, ready for reconnection\n");
    return 0;
}
```

#### 5.1.4 算法端健壮性写入

```c
// 算法端健壮性数据写入
int robust_write_frame(const uint8_t* image_data, const MaskHeader* mask_header,
                      const MaskInfo* mask_infos, const uint8_t* rle_data,
                      size_t total_mask_size) {
    SharedMemoryHeader* header = g_shared_memory;

    // 1. 检查App进程存活状态
    int64_t current_time = get_current_time_ms();
    if (current_time - header->app_heartbeat > 10000) {  // 10秒超时
        printf("App process appears to be dead\n");

        if (header->app_pid > 0 && kill(header->app_pid, 0) != 0) {
            // App进程死亡，等待重新连接
            printf("Waiting for app process reconnection...\n");
            sleep(1);
            return -1;  // 暂停数据写入，等待重连
        }
    }

    // 2. 健壮锁获取
    if (robust_mutex_lock(&header->global_mutex, "write_frame") != 0) {
        return -1;
    }

    // 3. 查找可用缓冲区
    uint32_t buffer_index = header->write_index % 4;
    FrameBuffer* buffer = &header->buffers[buffer_index];

    if (robust_mutex_lock(&buffer->mutex, "write_buffer") != 0) {
        pthread_mutex_unlock(&header->global_mutex);
        return -1;
    }

    // 4. 检查缓冲区状态，如果App处理太慢则跳过
    if (buffer->state != BUFFER_EMPTY && buffer->state != BUFFER_PROCESSED) {
        printf("Skipping frame due to slow app processing\n");
        pthread_mutex_unlock(&buffer->mutex);
        pthread_mutex_unlock(&header->global_mutex);
        return 0;  // 跳过这一帧，保持30fps节奏
    }

    // 5. 写入数据
    buffer->state = BUFFER_WRITING;
    // ... 数据写入逻辑 ...
    buffer->state = BUFFER_READY;

    // 6. 更新心跳和统计
    header->algorithm_heartbeat = current_time;
    header->frames_produced++;
    header->write_index++;

    // 7. 通知App端
    pthread_cond_signal(&header->data_ready_cond);

    pthread_mutex_unlock(&buffer->mutex);
    pthread_mutex_unlock(&header->global_mutex);

    return 0;
}
```

#### 5.1.5 App 崩溃恢复机制

**问题分析**：
App 崩溃后重启面临的挑战：

1. **共享内存丢失**：App 端创建的共享内存在崩溃后可能被系统清理
2. **算法端孤立**：算法进程仍在运行但无法与新 App 实例通信
3. **状态不一致**：新 App 实例无法获得之前的运行状态

**解决方案**：

```c
// App端崩溃恢复逻辑
int app_recovery_initialize(const std::string& shm_name) {
    int shm_fd;
    SharedMemoryHeader* header;

    // 1. 尝试连接到现有共享内存
    shm_fd = shm_open(shm_name.c_str(), O_RDWR, 0644);

    if (shm_fd >= 0) {
        // 共享内存仍然存在，尝试恢复连接
        printf("Found existing shared memory, attempting recovery...\n");

        header = (SharedMemoryHeader*)mmap(NULL, sizeof(SharedMemoryHeader),
                                         PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);

        if (header != MAP_FAILED) {
            // 2. 验证共享内存有效性
            if (header->magic_number == 0x4D4B5348 && header->version == CURRENT_VERSION) {
                // 3. 检查算法进程是否仍在运行
                if (header->algorithm_pid > 0 && kill(header->algorithm_pid, 0) == 0) {
                    // 算法进程仍在运行，执行恢复
                    return perform_app_recovery(header, shm_fd);
                } else {
                    printf("Algorithm process is dead, cleaning up shared memory\n");
                    cleanup_orphaned_shared_memory(header, shm_fd, shm_name);
                }
            } else {
                printf("Invalid shared memory format, recreating\n");
                munmap(header, sizeof(SharedMemoryHeader));
                close(shm_fd);
                shm_unlink(shm_name.c_str());
            }
        }
    }

    // 4. 创建新的共享内存
    printf("Creating new shared memory\n");
    return create_fresh_shared_memory(shm_name);
}

// 执行App恢复过程
int perform_app_recovery(SharedMemoryHeader* header, int shm_fd) {
    printf("Performing app recovery with active algorithm process\n");

    // 1. 重新初始化健壮互斥锁（如果需要）
    if (robust_mutex_lock(&header->global_mutex, "app_recovery") != 0) {
        printf("Failed to acquire global mutex during recovery\n");
        return -1;
    }

    // 2. 更新App进程信息
    header->app_pid = getpid();
    header->app_heartbeat = get_current_time_ms();
    header->recovery_count++;

    // 3. 清理可能的脏状态
    for (int i = 0; i < 4; i++) {
        FrameBuffer* buffer = &header->buffers[i];
        if (robust_mutex_lock(&buffer->mutex, "recovery_cleanup") == 0) {
            // 如果缓冲区处于读取状态，重置为就绪状态
            if (buffer->state == BUFFER_READING) {
                buffer->state = BUFFER_READY;
                printf("Reset buffer %d from READING to READY\n", i);
            }
            pthread_mutex_unlock(&buffer->mutex);
        }
    }

    // 4. 通知算法进程App已恢复
    header->cleanup_magic = 0x52455354; // "REST" - 表示恢复
    pthread_cond_broadcast(&header->data_ready_cond);
    pthread_cond_broadcast(&header->buffer_free_cond);

    pthread_mutex_unlock(&header->global_mutex);

    printf("App recovery completed successfully\n");
    return 0;
}

// 清理孤立的共享内存
void cleanup_orphaned_shared_memory(SharedMemoryHeader* header, int shm_fd,
                                   const std::string& shm_name) {
    printf("Cleaning up orphaned shared memory\n");

    // 销毁所有同步原语
    pthread_mutex_destroy(&header->global_mutex);
    pthread_cond_destroy(&header->data_ready_cond);
    pthread_cond_destroy(&header->buffer_free_cond);

    for (int i = 0; i < 4; i++) {
        pthread_mutex_destroy(&header->buffers[i].mutex);
    }

    munmap(header, sizeof(SharedMemoryHeader));
    close(shm_fd);
    shm_unlink(shm_name.c_str());
}
```

#### 5.1.6 算法端 App 恢复响应

```c
// 算法端检测App恢复的逻辑
int algorithm_handle_app_recovery() {
    SharedMemoryHeader* header = g_shared_memory;
    if (!header) return -1;

    // 检查恢复信号
    if (header->cleanup_magic == 0x52455354) { // "REST"
        printf("Detected app recovery, reinitializing connection\n");

        // 重置清理标记
        header->cleanup_magic = 0;

        // 验证新App进程
        if (header->app_pid > 0 && kill(header->app_pid, 0) == 0) {
            printf("New app process validated: PID %d\n", header->app_pid);

            // 清理可能的写入状态
            for (int i = 0; i < 4; i++) {
                FrameBuffer* buffer = &header->buffers[i];
                if (robust_mutex_lock(&buffer->mutex, "algorithm_recovery") == 0) {
                    if (buffer->state == BUFFER_WRITING) {
                        buffer->state = BUFFER_EMPTY;
                        printf("Reset buffer %d from WRITING to EMPTY\n", i);
                    }
                    pthread_mutex_unlock(&buffer->mutex);
                }
            }

            // 重置写入索引，重新开始
            header->write_index = 0;
            printf("Algorithm recovery completed, resuming 30fps operation\n");
            return 0;
        }
    }

    return -1;
}

// 修改算法端主循环，集成恢复检测
int algorithm_main_loop() {
    while (g_running) {
        // 1. 检查App恢复信号
        algorithm_handle_app_recovery();

        // 2. 正常的30fps数据生产
        if (robust_write_frame(image_data, mask_header, mask_infos, rle_data, total_size) < 0) {
            // 写入失败，可能是App崩溃了
            printf("Write failed, waiting for app recovery...\n");
            sleep(1);
            continue;
        }

        // 3. 精确等待33ms
        usleep(33333);
    }
}
```

#### 5.1.7 Flutter 端集成恢复逻辑

```dart
// lib/algorithm_ipc/algorithm_ipc.dart
class AlgorithmIpc {
  // 添加恢复标记
  bool _isRecovering = false;
  int _lastRecoveryCount = 0;

  // 初始化时尝试恢复
  bool initialize(String shmName) {
    try {
      final shmNamePtr = shmName.toNativeUtf8();

      // 使用恢复初始化函数
      _ipcManagerRef = _bindings.algorithmIpcRecoveryCreate(shmNamePtr.cast());
      calloc.free(shmNamePtr);

      if (_ipcManagerRef != ffi.nullptr) {
        _checkForRecovery();
        _startFrameReceiving();
        return true;
      }
      return false;
    } catch (e) {
      print('AlgorithmIpc initialization/recovery failed: $e');
      return false;
    }
  }

  // 检查是否为恢复启动
  void _checkForRecovery() {
    final recoveryCount = _bindings.algorithmIpcGetRecoveryCount(_ipcManagerRef!);
    if (recoveryCount > 0) {
      print('App recovery detected, recovery count: $recoveryCount');
      _isRecovering = true;
      _lastRecoveryCount = recoveryCount;

      // 通知UI层恢复状态
      _frameController?.add(AlgorithmFrame.recovery(recoveryCount));
    }
  }

  // 帧接收循环中添加恢复检测
  void _frameReceiveLoop() {
    while (_isActive) {
      try {
        final framePtr = _bindings.algorithmIpcReceiveFrame(_ipcManagerRef!, 1000);

        if (framePtr != ffi.nullptr) {
          final frame = _parseFrameData(framePtr);

          // 检查是否有新的恢复事件
          final currentRecoveryCount = _bindings.algorithmIpcGetRecoveryCount(_ipcManagerRef!);
          if (currentRecoveryCount > _lastRecoveryCount) {
            print('Detected recovery during operation');
            _lastRecoveryCount = currentRecoveryCount;
            _frameController?.add(AlgorithmFrame.recovery(currentRecoveryCount));
          }

          _frameController?.add(frame);
          _bindings.frameDataDestroy(framePtr);
        }
      } catch (e) {
        print('Frame receive error: $e');
        await Future.delayed(Duration(milliseconds: 100));
      }
    }
  }
}

// 恢复帧数据模型
class AlgorithmFrame {
  // 添加恢复构造器
  AlgorithmFrame.recovery(int recoveryCount)
    : frameId = -1,
      timestamp = DateTime.now().millisecondsSinceEpoch,
      width = 0,
      height = 0,
      imageData = Uint8List(0),
      masks = [],
      isRecoveryFrame = true,
      recoveryCount = recoveryCount;

  final bool isRecoveryFrame;
  final int recoveryCount;

  // ... 其他字段保持不变
}
```

#### 5.1.8 恢复流程总结

**App 崩溃恢复完整流程**：

1. **App 重启检测**：

   - 尝试连接现有共享内存
   - 验证算法进程存活状态
   - 判断是恢复模式还是全新启动

2. **状态同步**：

   - 清理脏缓冲区状态
   - 重置进程 PID 和心跳
   - 发送恢复信号给算法端

3. **算法端响应**：

   - 检测恢复信号
   - 清理可能的写入状态
   - 重置数据生产索引

4. **恢复完成**：
   - 双方恢复正常 30fps 数据流
   - UI 显示恢复状态通知
   - 统计恢复次数

**关键优势**：

- **无数据丢失**：算法进程持续运行，App 恢复后立即接收最新数据
- **自动检测**：无需人工干预，系统自动识别和处理恢复
- **状态清理**：自动清理崩溃时的不一致状态
- **用户友好**：UI 层获得恢复通知，可以显示恢复状态

#### 5.1.9 优雅关闭机制

```c
// App端优雅关闭（更新版本，支持恢复机制）
void graceful_shutdown_app(AlgorithmIpcManager* manager) {
    if (!manager || !manager->shm_header_) return;

    SharedMemoryHeader* header = manager->shm_header_;

    // 1. 设置关闭标记
    header->cleanup_magic = 0xDEADBEEF;
    header->app_heartbeat = 0;

    // 2. 广播条件变量，唤醒等待的算法进程
    pthread_cond_broadcast(&header->data_ready_cond);
    pthread_cond_broadcast(&header->buffer_free_cond);

    // 3. 等待算法进程响应（最多等待2秒）
    sleep(2);

    // 4. 检查是否为崩溃恢复模式（如果是，保留共享内存）
    bool preserve_memory = (header->recovery_count > 0) &&
                          (header->algorithm_pid > 0) &&
                          (kill(header->algorithm_pid, 0) == 0);

    if (!preserve_memory) {
        // 5. 销毁同步原语（仅在非恢复模式）
        pthread_mutex_destroy(&header->global_mutex);
        pthread_cond_destroy(&header->data_ready_cond);
        pthread_cond_destroy(&header->buffer_free_cond);

        for (int i = 0; i < 4; i++) {
            pthread_mutex_destroy(&header->buffers[i].mutex);
        }

        // 6. 删除共享内存
        shm_unlink(manager->shm_name_.c_str());
    } else {
        printf("Preserving shared memory for potential recovery\n");
    }

    // 7. 解除内存映射
    munmap(header, sizeof(SharedMemoryHeader));
}

// 算法端优雅关闭
void graceful_shutdown_algorithm() {
    if (!g_shared_memory) return;

    SharedMemoryHeader* header = g_shared_memory;

    // 1. 检查是否App端要求关闭
    if (header->cleanup_magic == 0xDEADBEEF) {
        printf("Received shutdown signal from app\n");
    }

    // 2. 清理心跳
    header->algorithm_heartbeat = 0;
    header->algorithm_pid = 0;

    // 3. 广播条件变量
    pthread_cond_broadcast(&header->buffer_free_cond);

    // 4. 解除内存映射
    munmap(header, sizeof(SharedMemoryHeader));
    g_shared_memory = NULL;
}
```

### 5.2 算法进程崩溃恢复机制

**问题分析**：
算法进程崩溃后面临的挑战：

1. **数据流中断**：App 端接收不到新的 30fps 数据流
2. **共享内存状态异常**：可能存在 BUFFER_WRITING 状态的脏数据
3. **算法重启连接**：新算法进程需要连接到现有共享内存

**解决方案**：

#### 5.2.1 App 端算法崩溃检测

```c
// App端检测算法进程崩溃
int detect_algorithm_crash(AlgorithmIpcManager* manager) {
    SharedMemoryHeader* header = manager->shm_header_;
    int64_t current_time = get_current_time_ms();

    // 1. 心跳超时检测（5秒无心跳）
    if (current_time - header->algorithm_heartbeat > 5000) {
        // 2. 验证进程是否真的死亡
        if (header->algorithm_pid > 0 && kill(header->algorithm_pid, 0) != 0) {
            printf("Algorithm process crash detected: PID %d\n", header->algorithm_pid);
            return handle_algorithm_crash(manager);
        }
    }

    return 0;
}

// 处理算法进程崩溃
int handle_algorithm_crash(AlgorithmIpcManager* manager) {
    SharedMemoryHeader* header = manager->shm_header_;

    printf("Handling algorithm process crash...\n");

    // 1. 清理所有缓冲区的写入状态
    for (int i = 0; i < 4; i++) {
        FrameBuffer* buffer = &header->buffers[i];
        if (robust_mutex_lock(&buffer->mutex, "crash_cleanup") == 0) {
            if (buffer->state == BUFFER_WRITING) {
                buffer->state = BUFFER_EMPTY;
                printf("Reset buffer %d from WRITING to EMPTY after crash\n", i);
            }
            pthread_mutex_unlock(&buffer->mutex);
        }
    }

    // 2. 重置算法进程信息
    header->algorithm_pid = 0;
    header->algorithm_heartbeat = 0;
    header->write_index = 0;
    header->recovery_count++;

    // 3. 设置等待重连标记
    header->cleanup_magic = 0x57414954; // "WAIT" - 等待算法重连

    // 4. 广播条件变量，唤醒可能阻塞的线程
    pthread_cond_broadcast(&header->data_ready_cond);
    pthread_cond_broadcast(&header->buffer_free_cond);

    printf("Algorithm crash handled, waiting for reconnection\n");
    return 0;
}

// App端监听算法重连
int wait_for_algorithm_reconnection(AlgorithmIpcManager* manager) {
    SharedMemoryHeader* header = manager->shm_header_;

    while (header->cleanup_magic == 0x57414954) { // "WAIT"
        // 检查是否有新的算法进程连接
        if (header->algorithm_pid > 0 &&
            header->algorithm_heartbeat > 0 &&
            kill(header->algorithm_pid, 0) == 0) {

            printf("Algorithm process reconnected: PID %d\n", header->algorithm_pid);

            // 清理等待标记
            header->cleanup_magic = 0;

            // 通知UI层算法已重连
            return 1; // 重连成功
        }

        usleep(100000); // 100ms检查间隔
    }

    return 0;
}
```

#### 5.2.2 算法端崩溃重连机制

```c
// 算法进程重启后的连接逻辑
int algorithm_reconnect_after_crash(const char* shm_name) {
    printf("Attempting to reconnect after crash...\n");

    // 1. 连接到现有共享内存
    int shm_fd = shm_open(shm_name, O_RDWR, 0644);
    if (shm_fd < 0) {
        printf("Failed to connect to existing shared memory: %s\n", strerror(errno));
        return -1;
    }

    // 2. 映射共享内存
    SharedMemoryHeader* header = (SharedMemoryHeader*)mmap(NULL, sizeof(SharedMemoryHeader),
                                                          PROT_READ | PROT_WRITE, MAP_SHARED, shm_fd, 0);
    if (header == MAP_FAILED) {
        printf("Failed to map shared memory: %s\n", strerror(errno));
        close(shm_fd);
        return -1;
    }

    // 3. 验证共享内存有效性
    if (header->magic_number != 0x4D4B5348 ||
        header->version != CURRENT_VERSION) {
        printf("Invalid shared memory format\n");
        munmap(header, sizeof(SharedMemoryHeader));
        close(shm_fd);
        return -1;
    }

    // 4. 检查App进程是否仍在运行
    if (header->app_pid <= 0 || kill(header->app_pid, 0) != 0) {
        printf("App process is not running, cannot reconnect\n");
        munmap(header, sizeof(SharedMemoryHeader));
        close(shm_fd);
        return -1;
    }

    // 5. 执行重连初始化
    return perform_algorithm_reconnection(header);
}

// 执行算法重连过程
int perform_algorithm_reconnection(SharedMemoryHeader* header) {
    printf("Performing algorithm reconnection...\n");

    // 1. 获取全局锁
    if (robust_mutex_lock(&header->global_mutex, "algorithm_reconnect") != 0) {
        printf("Failed to acquire global mutex during reconnection\n");
        return -1;
    }

    // 2. 更新算法进程信息
    header->algorithm_pid = getpid();
    header->algorithm_heartbeat = get_current_time_ms();

    // 3. 检查是否App在等待重连
    if (header->cleanup_magic == 0x57414954) { // "WAIT"
        printf("App is waiting for reconnection, proceeding...\n");

        // 清理等待标记
        header->cleanup_magic = 0;

        // 重置写入状态
        header->write_index = 0;

        // 清理可能的脏缓冲区
        for (int i = 0; i < 4; i++) {
            FrameBuffer* buffer = &header->buffers[i];
            if (robust_mutex_lock(&buffer->mutex, "reconnect_cleanup") == 0) {
                if (buffer->state == BUFFER_WRITING) {
                    buffer->state = BUFFER_EMPTY;
                }
                pthread_mutex_unlock(&buffer->mutex);
            }
        }
    }

    // 4. 通知App算法已重连
    pthread_cond_broadcast(&header->data_ready_cond);
    pthread_cond_broadcast(&header->buffer_free_cond);

    pthread_mutex_unlock(&header->global_mutex);

    // 5. 设置全局引用
    g_shared_memory = header;

    printf("Algorithm reconnection completed successfully\n");
    return 0;
}

// 修改算法端主函数，支持重连
int main() {
    const char* shm_name = "/medias_kit_algorithm_shm";

    // 首先尝试重连（如果是崩溃重启）
    if (algorithm_reconnect_after_crash(shm_name) == 0) {
        printf("Reconnected successfully, resuming operation\n");
    } else {
        // 如果重连失败，等待App创建新的共享内存
        printf("Reconnection failed, waiting for fresh connection\n");
        while (algorithm_shm_connect(shm_name) != 0) {
            sleep(1);
        }
    }

    // 开始正常的30fps数据生产循环
    return algorithm_main_loop();
}
```

#### 5.2.3 Flutter 端算法崩溃处理

```dart
// lib/algorithm_ipc/algorithm_ipc.dart
class AlgorithmIpc {
  Timer? _crashDetectionTimer;
  bool _algorithmCrashed = false;

  void _startCrashDetection() {
    _crashDetectionTimer = Timer.periodic(Duration(seconds: 2), (timer) {
      if (_ipcManagerRef != null) {
        final crashStatus = _bindings.algorithmIpcCheckCrashStatus(_ipcManagerRef!);

        if (crashStatus == 1 && !_algorithmCrashed) {
          // 算法进程崩溃
          _algorithmCrashed = true;
          print('Algorithm process crashed, waiting for reconnection...');

          // 通知UI层算法崩溃
          _frameController?.add(AlgorithmFrame.algorithmCrash());

          // 开始等待重连
          _waitForAlgorithmReconnection();
        }
      }
    });
  }

  void _waitForAlgorithmReconnection() async {
    print('Waiting for algorithm process reconnection...');

    while (_algorithmCrashed && _isActive) {
      await Future.delayed(Duration(milliseconds: 500));

      if (_ipcManagerRef != null) {
        final reconnectStatus = _bindings.algorithmIpcCheckReconnection(_ipcManagerRef!);

        if (reconnectStatus == 1) {
          // 算法重连成功
          _algorithmCrashed = false;
          print('Algorithm process reconnected successfully');

          // 通知UI层算法已重连
          _frameController?.add(AlgorithmFrame.algorithmReconnected());

          // 恢复正常帧接收
          break;
        }
      }
    }
  }

  @override
  void dispose() {
    _crashDetectionTimer?.cancel();
    super.dispose();
  }
}

// 扩展AlgorithmFrame支持崩溃和重连状态
class AlgorithmFrame {
  // 算法崩溃构造器
  AlgorithmFrame.algorithmCrash()
    : frameId = -2,
      timestamp = DateTime.now().millisecondsSinceEpoch,
      width = 0,
      height = 0,
      imageData = Uint8List(0),
      masks = [],
      isAlgorithmCrash = true,
      isAlgorithmReconnected = false;

  // 算法重连构造器
  AlgorithmFrame.algorithmReconnected()
    : frameId = -3,
      timestamp = DateTime.now().millisecondsSinceEpoch,
      width = 0,
      height = 0,
      imageData = Uint8List(0),
      masks = [],
      isAlgorithmCrash = false,
      isAlgorithmReconnected = true;

  final bool isAlgorithmCrash;
  final bool isAlgorithmReconnected;

  // ... 其他字段保持不变
}
```

#### 5.2.4 UI 层崩溃状态处理

```dart
// example/lib/algorithm_demo.dart
class _AlgorithmDemoState extends State<AlgorithmDemo> {
  bool _algorithmConnected = true;
  String _connectionStatus = "Connected";

  void _onFrameReceived(AlgorithmFrame frame) {
    if (frame.isAlgorithmCrash) {
      setState(() {
        _algorithmConnected = false;
        _connectionStatus = "Algorithm Crashed - Waiting for reconnection...";
      });

      // 显示崩溃恢复提示
      _showCrashRecoveryDialog();

    } else if (frame.isAlgorithmReconnected) {
      setState(() {
        _algorithmConnected = true;
        _connectionStatus = "Algorithm Reconnected - Resuming...";
      });

      // 3秒后恢复正常状态显示
      Timer(Duration(seconds: 3), () {
        setState(() {
          _connectionStatus = "Connected";
        });
      });

    } else if (frame.isRecoveryFrame) {
      // App恢复状态处理
      setState(() {
        _connectionStatus = "App Recovered (${frame.recoveryCount} times)";
      });

    } else {
      // 正常帧数据处理
      _processNormalFrame(frame);
    }
  }

  void _showCrashRecoveryDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text('Algorithm Process Recovery'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Algorithm process has crashed.\nWaiting for automatic recovery...'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Continue Waiting'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Algorithm IPC Demo'),
        backgroundColor: _algorithmConnected ? Colors.green : Colors.orange,
      ),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            color: _algorithmConnected ? Colors.green.shade50 : Colors.orange.shade50,
            child: Row(
              children: [
                Icon(
                  _algorithmConnected ? Icons.check_circle : Icons.warning,
                  color: _algorithmConnected ? Colors.green : Colors.orange,
                ),
                SizedBox(width: 8),
                Text(_connectionStatus),
              ],
            ),
          ),
          // ... 其他UI组件
        ],
      ),
    );
  }
}
```

## 6. Python3 算法侧实现

### 6.1 Python 共享内存接口设计

算法侧使用 Python3 实现，通过 mmap 和 ctypes 与共享内存交互：

```python
# algorithm_ipc.py - Python3算法端共享内存接口

import mmap
import os
import time
import struct
import ctypes
from ctypes import Structure, c_uint32, c_uint8, c_int64, c_void_p, POINTER
from typing import List, Tuple, Optional
import threading
import signal
import numpy as np

# 引入pthread库用于条件变量操作
libc = ctypes.CDLL("libc.so.6")
libpthread = ctypes.CDLL("libpthread.so.0")

# 数据结构定义（对应C结构体）
class FrameMetadata(Structure):
    _fields_ = [
        ("timestamp", c_int64),      # 时间戳（毫秒）
        ("frame_id", c_uint32),      # 帧序号
        ("width", c_uint32),         # 图像宽度
        ("height", c_uint32),        # 图像高度
        ("image_size", c_uint32),    # 图像数据大小
        ("mask_size", c_uint32),     # mask数据大小
        ("reserved", c_uint8 * 4),   # 字节对齐保留
    ]

class MaskHeader(Structure):
    _fields_ = [
        ("mask_count", c_uint32),    # mask数量
        ("mask_width", c_uint32),    # mask宽度
        ("mask_height", c_uint32),   # mask高度
        ("reserved", c_uint8 * 4),   # 字节对齐保留
    ]

class MaskInfo(Structure):
    _fields_ = [
        ("object_id", c_uint32),           # mask唯一标识
        ("rle_offset", c_uint32),        # RLE数据偏移量
        ("rle_counts_length", c_uint32), # RLE counts数组长度
        ("original_width", c_uint32),    # 原始mask宽度
        ("original_height", c_uint32),   # 原始mask高度
        ("tissue_id", c_uint32),        # 渲染颜色 RGBA格式
        ("reserved", c_uint8 * 4),       # 字节对齐保留
    ]

# 缓冲区状态枚举
class BufferState:
    EMPTY = 0
    WRITING = 1
    READY = 2
    READING = 3
    PROCESSED = 4

class AlgorithmIpc:
    """Python3算法端IPC管理器"""

    def __init__(self):
        self.shm_fd = None
        self.shared_memory = None
        self.shm_name = None
        self.frame_counter = 0
        self.running = False
        self._lock = threading.Lock()

    def connect(self, shm_name: str) -> bool:
        """连接到App创建的共享内存"""
        try:
            self.shm_name = shm_name

            # 1. 打开共享内存
            self.shm_fd = os.open(f"/dev/shm{shm_name}", os.O_RDWR)

            # 2. 映射共享内存
            self.shared_memory = mmap.mmap(
                self.shm_fd, 44040192,  # 42MB
                mmap.MAP_SHARED, mmap.PROT_READ | mmap.PROT_WRITE
            )

            # 3. 验证魔数
            magic_number = struct.unpack_from('I', self.shared_memory, 0)[0]
            if magic_number != 0x4D4B5348:
                raise ValueError("Invalid shared memory format")

            # 4. 更新算法进程信息
            self._update_process_info()

            print(f"Connected to shared memory: {shm_name}")
            return True

        except Exception as e:
            print(f"Failed to connect to shared memory: {e}")
            return False

    def reconnect_after_crash(self, shm_name: str) -> bool:
        """崩溃后重连机制"""
        try:
            print("Attempting to reconnect after crash...")

            if self.connect(shm_name):
                # 检查App进程是否仍在运行
                app_pid = struct.unpack_from('I', self.shared_memory, 40)[0]  # app_pid offset
                if app_pid > 0:
                    try:
                        os.kill(app_pid, 0)  # 检查进程存在
                        self._handle_reconnection()
                        return True
                    except OSError:
                        print("App process is not running")
                        return False
            return False

        except Exception as e:
            print(f"Reconnection failed: {e}")
            return False

    def write_frame(self, image_data: np.ndarray, masks: List[dict],
                   timestamp_ms: int) -> bool:
        """写入一帧数据到共享内存"""
        if not self.shared_memory:
            return False

        try:
            with self._lock:
                # 1. 获取可用缓冲区
                buffer_index = self._get_available_buffer()
                if buffer_index < 0:
                    print("No available buffer, skipping frame")
                    return True  # 跳过帧，保持30fps节奏

                # 2. 准备mask数据
                mask_data, mask_header, mask_infos = self._prepare_mask_data(masks)
                if len(mask_data) > 2097152:  # 2MB限制
                    print(f"Mask data too large: {len(mask_data)} bytes, dropping masks")
                    mask_data, mask_header, mask_infos = self._apply_size_limit(masks)

                # 3. 写入数据到缓冲区
                self._write_to_buffer(buffer_index, image_data, mask_data,
                                    mask_header, mask_infos, timestamp_ms)

                # 4. 通知App端
                self._notify_app()

                # 5. 更新心跳
                self._update_heartbeat()

                return True

        except Exception as e:
            print(f"Write frame failed: {e}")
            return False

    def _update_process_info(self):
        """更新算法进程信息"""
        current_time = int(time.time() * 1000)
        pid = os.getpid()

        # 更新PID和心跳
        # algorithm_heartbeat偏移: 20 (after write_index + read_index)
        # algorithm_pid偏移: 36 (after algorithm_heartbeat + app_heartbeat)
        struct.pack_into('Q', self.shared_memory, 20, current_time)  # algorithm_heartbeat
        struct.pack_into('I', self.shared_memory, 36, pid)          # algorithm_pid

    def _handle_reconnection(self):
        """处理重连恢复逻辑"""
        # 检查等待重连标记 (cleanup_magic at offset 44)
        cleanup_magic = struct.unpack_from('I', self.shared_memory, 44)[0]

        if cleanup_magic == 0x57414954:  # "WAIT"
            print("App is waiting for reconnection")

            # 清理等待标记
            struct.pack_into('I', self.shared_memory, 44, 0)

            # 重置写入索引
            struct.pack_into('I', self.shared_memory, 12, 0)

            # 清理脏缓冲区状态
            self._cleanup_dirty_buffers()

            # 广播条件变量通知App重连成功
            data_ready_cond_addr = ctypes.addressof(ctypes.c_char.from_buffer(self.shared_memory, 88))
            buffer_free_cond_addr = ctypes.addressof(ctypes.c_char.from_buffer(self.shared_memory, 136))
            libpthread.pthread_cond_broadcast(data_ready_cond_addr)
            libpthread.pthread_cond_broadcast(buffer_free_cond_addr)

            print("Reconnection completed successfully")

    def _get_available_buffer(self) -> int:
        """获取可用缓冲区索引"""
        write_index = struct.unpack_from('I', self.shared_memory, 12)[0]
        buffer_index = write_index % 4

        # 检查缓冲区状态 (每个缓冲区10.5MB，state在offset+32)
        buffer_offset = 1024 + buffer_index * 11010048
        state = struct.unpack_from('I', self.shared_memory, buffer_offset + 32)[0]

        if state not in [BufferState.EMPTY, BufferState.PROCESSED]:
            return -1  # 缓冲区忙，跳过这帧

        return buffer_index

    def _prepare_mask_data(self, masks: List[dict]) -> Tuple[bytes, MaskHeader, List[MaskInfo]]:
        """准备mask数据，转换为RLE格式

        重要：算法内部已将masks按优先级排序（低到高），数组顺序即为渲染优先级
        """

        mask_infos = []
        rle_data = b''

        for i, mask in enumerate(masks):
            # 假设mask包含binary_mask和color信息
            binary_mask = mask['binary_mask']  # numpy array
            tissue_id = mask.get('tissue_id', 0xFF0000FF)

            # 编码为RLE
            rle_counts = self._encode_rle(binary_mask)
            rle_bytes = struct.pack(f'{len(rle_counts)}I', *rle_counts)

            # 创建mask信息
            mask_info = MaskInfo(
                object_id=mask.get('object_id', i),
                rle_offset=len(rle_data),
                rle_counts_length=len(rle_counts),
                original_width=binary_mask.shape[1],
                original_height=binary_mask.shape[0],
                tissue_id=tissue_id
            )

            mask_infos.append(mask_info)
            rle_data += rle_bytes

        # 创建mask header
        mask_header = MaskHeader(
            mask_count=len(masks),
            mask_width=1920,
            mask_height=1080
        )

        return rle_data, mask_header, mask_infos

    def _encode_rle(self, binary_mask: np.ndarray) -> List[int]:
        """将二进制mask编码为RLE格式（兼容SAM2）"""
        # 按列优先顺序处理（Fortran order）
        flat_mask = binary_mask.T.flatten()  # 转置后拉平

        # RLE编码
        counts = []
        current_value = 0
        current_count = 0

        for pixel in flat_mask:
            pixel_value = 1 if pixel > 0 else 0
            if pixel_value == current_value:
                current_count += 1
            else:
                counts.append(current_count)
                current_count = 1
                current_value = pixel_value

        counts.append(current_count)

        # 如果第一个像素是前景，在开头添加0
        if flat_mask[0] > 0:
            counts.insert(0, 0)

        return counts

    def _apply_size_limit(self, masks: List[dict]) -> Tuple[bytes, MaskHeader, List[MaskInfo]]:
        """应用2MB大小限制，按面积排序丢弃小mask"""
        # 按mask面积排序
        sorted_masks = sorted(masks,
                            key=lambda m: m['binary_mask'].sum(),
                            reverse=True)

        # 逐步添加直到达到2MB限制
        selected_masks = []
        total_size = ctypes.sizeof(MaskHeader)

        for mask in sorted_masks:
            rle_counts = self._encode_rle(mask['binary_mask'])
            needed_size = ctypes.sizeof(MaskInfo) + len(rle_counts) * 4

            if total_size + needed_size <= 2096128:  # 2MB - 1KB预留
                selected_masks.append(mask)
                total_size += needed_size
            else:
                print(f"Dropped mask due to size limit")
                break

        return self._prepare_mask_data(selected_masks)

    def _write_to_buffer(self, buffer_index: int, image_data: np.ndarray,
                        mask_data: bytes, mask_header: MaskHeader,
                        mask_infos: List[MaskInfo], timestamp_ms: int):
        """写入数据到指定缓冲区"""
        buffer_offset = 1024 + buffer_index * 11010048

        # 1. 设置状态为WRITING（state现在在offset+32）
        struct.pack_into('I', self.shared_memory, buffer_offset + 32, BufferState.WRITING)

        # 2. 写入帧元数据（metadata现在在buffer开始处）
        metadata_offset = buffer_offset
        struct.pack_into('Q', self.shared_memory, metadata_offset, timestamp_ms)
        struct.pack_into('I', self.shared_memory, metadata_offset + 8, self.frame_counter)
        struct.pack_into('I', self.shared_memory, metadata_offset + 12, 1920)
        struct.pack_into('I', self.shared_memory, metadata_offset + 16, 1080)
        struct.pack_into('I', self.shared_memory, metadata_offset + 20, len(image_data))
        struct.pack_into('I', self.shared_memory, metadata_offset + 24, len(mask_data))

        # 3. 写入图像数据（image_data现在在offset+40）
        image_offset = buffer_offset + 40
        self.shared_memory[image_offset:image_offset + len(image_data)] = image_data.tobytes()

        # 4. 写入mask数据大小（mask_data_size现在在offset+36）
        mask_size_offset = buffer_offset + 36
        struct.pack_into('I', self.shared_memory, mask_size_offset, len(mask_data))

        # 5. 写入mask header（mask_data现在在image_data之后）
        mask_data_offset = image_offset + 8294400
        ctypes.memmove(mask_data_offset, ctypes.byref(mask_header), ctypes.sizeof(mask_header))

        # 6. 写入mask infos数组
        current_offset = mask_data_offset + ctypes.sizeof(mask_header)
        for mask_info in mask_infos:
            ctypes.memmove(current_offset, ctypes.byref(mask_info), ctypes.sizeof(mask_info))
            current_offset += ctypes.sizeof(mask_info)

        # 7. 写入RLE数据
        self.shared_memory[current_offset:current_offset + len(mask_data)] = mask_data

        # 8. 设置状态为READY（state在offset+32）
        struct.pack_into('I', self.shared_memory, buffer_offset + 32, BufferState.READY)

        self.frame_counter += 1

    def _notify_app(self):
        """通知App端有新数据"""
        # 更新写入索引
        write_index = struct.unpack_from('I', self.shared_memory, 12)[0]
        struct.pack_into('I', self.shared_memory, 12, write_index + 1)

        # 更新统计信息 (frames_produced在184偏移处)
        frames_produced = struct.unpack_from('Q', self.shared_memory, 184)[0]
        struct.pack_into('Q', self.shared_memory, 184, frames_produced + 1)

        # 通知App端有新数据（发送条件变量信号）
        # global_mutex: 48, data_ready_cond: 48+40=88, buffer_free_cond: 88+48=136
        data_ready_cond_addr = ctypes.addressof(ctypes.c_char.from_buffer(self.shared_memory, 88))
        libpthread.pthread_cond_signal(data_ready_cond_addr)

    def _update_heartbeat(self):
        """更新心跳时间戳"""
        current_time = int(time.time() * 1000)
        struct.pack_into('Q', self.shared_memory, 20, current_time)

    def _cleanup_dirty_buffers(self):
        """清理脏缓冲区状态"""
        for i in range(4):
            buffer_offset = 1024 + i * 11010048
            state = struct.unpack_from('I', self.shared_memory, buffer_offset + 32)[0]
            if state == BufferState.WRITING:
                struct.pack_into('I', self.shared_memory, buffer_offset + 32, BufferState.EMPTY)
                print(f"Reset buffer {i} from WRITING to EMPTY")

    def cleanup(self):
        """清理资源"""
        if self.shared_memory:
            # 清理心跳和PID
            struct.pack_into('Q', self.shared_memory, 20, 0)  # algorithm_heartbeat
            struct.pack_into('I', self.shared_memory, 36, 0)  # algorithm_pid

            self.shared_memory.close()
            self.shared_memory = None

        if self.shm_fd:
            os.close(self.shm_fd)
            self.shm_fd = None

# RLE工具函数（兼容SAM2）
def encode_masks_to_rle(masks: List[np.ndarray]) -> List[dict]:
    """将numpy mask数组编码为RLE格式（兼容SAM2）"""
    rle_masks = []
    for i, mask in enumerate(masks):
        rle_counts = AlgorithmIpc()._encode_rle(mask)
        rle_mask = {
            'object_id': i,
            'binary_mask': mask,
            'rle_counts': rle_counts,
            'size': [mask.shape[0], mask.shape[1]],
            'tissue_id': 0xFF0000FF + (i * 0x100000)  # 不同颜色
        }
        rle_masks.append(rle_mask)
    return rle_masks
```

### 6.2 Python 算法主程序示例

```python
#!/usr/bin/env python3
# algorithm_main.py - Python3算法主程序

import time
import signal
import sys
import numpy as np
from typing import List
from algorithm_ipc import AlgorithmIpc, encode_masks_to_rle

class SegmentationAlgorithm:
    """分割算法主类"""

    def __init__(self):
        self.ipc = AlgorithmIpc()
        self.running = False
        self.frame_interval = 1.0 / 30.0  # 30fps = 33.33ms

    def initialize(self, shm_name: str = "/medias_kit_algorithm_shm") -> bool:
        """初始化连接"""
        # 首先尝试重连（崩溃重启场景）
        if self.ipc.reconnect_after_crash(shm_name):
            print("Reconnected after crash, resuming operation")
            return True

        # 重连失败，尝试正常连接
        retry_count = 0
        while retry_count < 30:  # 最多重试30秒
            if self.ipc.connect(shm_name):
                print("Connected to shared memory successfully")
                return True

            print(f"Connection attempt {retry_count + 1}/30 failed, retrying...")
            time.sleep(1)
            retry_count += 1

        print("Failed to connect to shared memory after 30 retries")
        return False

    def run_segmentation_loop(self):
        """30fps分割主循环"""
        self.running = True
        frame_count = 0

        print("Starting 30fps segmentation loop...")

        while self.running:
            loop_start = time.time()

            try:
                # 1. 采集图像数据 (模拟或从摄像头获取)
                image_data = self.capture_image()

                # 2. 执行算法识别分割
                masks = self.perform_segmentation(image_data)

                # 3. 编码为RLE格式（兼容SAM2）
                rle_masks = encode_masks_to_rle(masks)

                # 4. 写入共享内存
                timestamp_ms = int(time.time() * 1000)
                success = self.ipc.write_frame(image_data, rle_masks, timestamp_ms)

                if success:
                    frame_count += 1
                    if frame_count % 30 == 0:  # 每秒输出一次统计
                        print(f"Processed {frame_count} frames, {len(rle_masks)} masks in current frame")
                else:
                    print("Failed to write frame to shared memory")

            except Exception as e:
                print(f"Error in segmentation loop: {e}")
                continue

            # 5. 精确30fps时间控制
            elapsed = time.time() - loop_start
            sleep_time = max(0, self.frame_interval - elapsed)
            if sleep_time > 0:
                time.sleep(sleep_time)
            else:
                print(f"Warning: Frame processing took {elapsed:.3f}s, exceeding 30fps target")

    def capture_image(self) -> np.ndarray:
        """图像采集（需要根据实际情况实现）"""
        # 这里是示例，实际应该从摄像头或视频流获取
        # 返回1920x1080x4 RGBA格式的图像
        return np.random.randint(0, 255, (1080, 1920, 4), dtype=np.uint8)

    def perform_segmentation(self, image_data: np.ndarray) -> List[np.ndarray]:
        """执行分割分析（需要根据实际分割模型实现，可兼容SAM2）"""
        # 这里是示例，实际应该调用SAM2模型
        masks = []

        # 模拟生成几个随机mask
        num_masks = np.random.randint(1, 10)  # 1-9个mask

        for i in range(num_masks):
            # 生成随机的二进制mask
            center_x = np.random.randint(200, 1720)
            center_y = np.random.randint(200, 880)
            radius = np.random.randint(50, 200)

            mask = np.zeros((1080, 1920), dtype=np.uint8)
            y, x = np.ogrid[:1080, :1920]
            mask_area = (x - center_x)**2 + (y - center_y)**2 < radius**2
            mask[mask_area] = 1

            masks.append(mask)

        return masks

    def cleanup(self):
        """清理资源"""
        self.running = False
        self.ipc.cleanup()
        print("Algorithm process cleaned up")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"Received signal {signum}, shutting down...")
    global algorithm
    if algorithm:
        algorithm.cleanup()
    sys.exit(0)

# 主程序入口
if __name__ == "__main__":
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # 创建算法实例
    algorithm = SegmentationAlgorithm()

    try:
        # 初始化连接
        if not algorithm.initialize():
            print("Failed to initialize algorithm connection")
            sys.exit(1)

        # 开始分割循环
        algorithm.run_segmentation_loop()

    except KeyboardInterrupt:
        print("Interrupted by user")
    except Exception as e:
        print(f"Unexpected error: {e}")
    finally:
        algorithm.cleanup()
```

### 6.3 分割模型集成示例（可选，兼容 SAM2）

```python
# segmentation_integration.py - 分割模型集成示例（兼容SAM2）

import torch
import numpy as np
from typing import List, Dict
# from sam2.build_sam import build_sam2  # SAM2兼容示例
# from sam2.sam2_image_predictor import SAM2ImagePredictor  # SAM2兼容示例

class SegmentationEngine:
    """SAM2分割引擎"""

    def __init__(self, model_cfg: str = "segmentation_model.yaml",
                 checkpoint_path: str = "segmentation_model.pt"):
        """初始化SAM2模型"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")

        # 构建SAM2模型
        # self.model = build_segmentation_model(model_cfg, checkpoint_path, device=self.device)  # 兼容SAM2
        # self.predictor = SegmentationPredictor(self.model)  # 兼容SAM2

        print("SAM2 model loaded successfully")

    def segment_image(self, image: np.ndarray,
                     points_per_side: int = 32) -> List[Dict]:
        """对图像进行自动分割"""
        try:
            # 设置图像
            self.predictor.set_image(image)

            # 使用自动mask生成器
            from sam2.automatic_mask_generator import SAM2AutomaticMaskGenerator

            mask_generator = SAM2AutomaticMaskGenerator(
                # model=self.model,  # 兼容SAM2
                points_per_side=points_per_side,
                pred_iou_thresh=0.8,
                stability_score_thresh=0.95,
                crop_n_layers=1,
                crop_n_points_downscale_factor=2,
                min_mask_region_area=100,
                output_mode="uncompressed_rle"
            )

            # 生成mask
            masks = mask_generator.generate(image)

            # 转换为我们需要的格式
            processed_masks = []
            for i, mask_data in enumerate(masks):
                # 从RLE解码为二进制mask
                rle = mask_data['segmentation']
                binary_mask = self._rle_to_mask(rle)

                processed_mask = {
                    'object_id': i,
                    'binary_mask': binary_mask,
                    'area': mask_data['area'],
                    'bbox': mask_data['bbox'],
                    'predicted_iou': mask_data['predicted_iou'],
                    'stability_score': mask_data['stability_score'],
                    'tissue_id': self._generate_color(i)
                }
                processed_masks.append(processed_mask)

            return processed_masks

        except Exception as e:
            print(f"SAM2 segmentation failed: {e}")
            return []

    def _rle_to_mask(self, rle: Dict) -> np.ndarray:
        """将RLE格式转换为二进制mask"""
        from sam2.utils.amg import rle_to_mask
        return rle_to_mask(rle).astype(np.uint8)

    def _generate_color(self, object_id: int) -> int:
        """为mask生成颜色"""
        # 生成RGBA颜色 (格式: 0xAABBGGRR)
        colors = [
            0xFF0000FF,  # 红色
            0xFF00FF00,  # 绿色
            0xFFFF0000,  # 蓝色
            0xFF00FFFF,  # 黄色
            0xFFFF00FF,  # 品红色
            0xFFFFFF00,  # 青色
            0xFF800080,  # 紫色
            0xFF008080,  # 橙色
        ]
        return colors[object_id % len(colors)]

# 更新后的算法主类
class AdvancedSegmentationAlgorithm(SegmentationAlgorithm):
    """使用高级分割模型的算法（可兼容SAM2）"""

    def __init__(self, model_cfg: str = "segmentation_model.yaml",
                 checkpoint_path: str = "segmentation_model.pt"):
        super().__init__()
        self.segmentation_engine = SegmentationEngine(model_cfg, checkpoint_path)

    def perform_segmentation(self, image_data: np.ndarray) -> List[np.ndarray]:
        """使用SAM2执行分割分析"""
        # 转换RGBA到RGB（SAM2期望RGB格式）
        rgb_image = image_data[:, :, :3]

        # 执行SAM2分割
        mask_results = self.segmentation_engine.segment_image(rgb_image)

        # 提取二进制mask
        masks = [result['binary_mask'] for result in mask_results]

        # 应用大小过滤（移除太小的mask）
        filtered_masks = []
        for mask in masks:
            if np.sum(mask) > 1000:  # 至少1000像素
                filtered_masks.append(mask)

        return filtered_masks[:20]  # 最多20个mask以控制数据大小
```

### 6.4 数据传递流程（Python 版本）

```text
Python算法进程流程（30fps稳定数据生产）：
1. 连接到App创建的共享内存 ipc.connect() 或 ipc.reconnect_after_crash()
2. 30fps循环处理（精确33.33ms间隔）:
   a. image_data = capture_image()  # 采集图像数据
   b. masks = perform_segmentation(image_data)  # 算法识别分割（内部已按优先级排序）
   c. rle_masks = encode_masks_to_rle(masks)  # 编码为RLE格式（兼容SAM2）
   d. 应用2MB大小限制，_apply_size_limit() 自动丢弃超出的mask
   e. ipc.write_frame(image_data, rle_masks, timestamp_ms)  # 写入RLE数据
   f. 自动通知app有新数据 (内部处理条件变量)
   g. time.sleep() 精确等待33.33ms后继续下一帧
3. 程序退出时调用 ipc.cleanup()

App进程流程（数据驱动即时渲染）：
1. 初始化IPC管理器，创建42MB共享内存
2. 启动数据监听线程
3. 数据监听循环：
   a. 等待算法端数据信号 (条件变量)
   b. 检测到新数据立即读取
   c. 解码RLE mask数据
   d. 立即更新纹理渲染
   e. 标记帧处理完成
   f. 继续监听下一帧数据
```

### 6.5 性能要求和数据限制

**性能要求**：

- **帧率保证**: 算法端以 30fps 稳定产生数据，App 端即时响应渲染
  - 算法端：稳定以 30fps 速度产生数据（每 33ms 一帧），确保数据流连续性
  - App 端：只要检测到新数据就立即渲染，无需等待固定时间间隔
  - 渲染策略：App 端采用数据驱动渲染，响应算法端的数据生产节奏
- **内存限制**: 每帧 mask 数据不得超过 2MB
- **响应机制**: App 端通过共享内存监听实现低延迟数据检测和渲染
- **资源清理**: 程序异常退出时自动清理共享内存资源

**数据丢弃策略**：
算法进程必须实现数据大小控制，确保 mask 数据不超过 2MB：

```c
// 数据大小控制策略
int algorithm_apply_size_limit(
    const uint8_t** mask_array,     // 输入mask数组
    uint32_t mask_count,            // mask数量
    uint32_t* selected_masks,       // 输出：选中的mask索引
    uint32_t* selected_count        // 输出：选中的mask数量
) {
    const uint32_t MAX_MASK_DATA_SIZE = 2097152 - 1024; // 预留1KB给头部

    // 1. 按面积大小排序，优先保留大对象
    uint32_t total_size = sizeof(MaskHeader);
    uint32_t count = 0;

    for (uint32_t i = 0; i < mask_count; i++) {
        uint32_t rle_size = estimate_rle_size(mask_array[i]);
        uint32_t needed_size = sizeof(MaskInfo) + rle_size;

        if (total_size + needed_size <= MAX_MASK_DATA_SIZE) {
            selected_masks[count++] = i;
            total_size += needed_size;
        } else {
            // 记录丢弃的mask数量（用于统计）
            log_dropped_mask_count++;
            break; // 超出限制，停止添加
        }
    }

    *selected_count = count;
    return 0;
}
```

**丢弃策略原则**：

1. **按面积排序**：优先保留面积大的 mask 对象
2. **严格限制**：超过 2MB 立即停止添加更多 mask
3. **简单高效**：避免复杂的优先级计算，保证实时性

### 6.6 Mask 数据格式要求（SAM2 标准）

算法侧输出的 mask 数据必须符合以下格式：

```c
// Mask数据布局（RLE压缩格式传递）
struct MaskDataLayout {
    MaskHeader header;          // mask头部信息（兼容SAM2）
    MaskInfo masks[MAX_MASKS];  // 每个mask的RLE信息数组（兼容SAM2）
    uint8_t rle_data[];             // RLE压缩数据（各mask的counts数组连续存储）
};
```

**数据格式规范（RLE 传递策略）**：

全程使用 RLE 压缩数据传递，提供最佳的性能和存储效率：

- **算法侧**：将二进制 mask 编码为 SAM2 未压缩 RLE 格式（counts 数组）
- **共享内存传递**: 直接传递 RLE counts 数据，避免存储大量 0 值像素
- **App 侧解码**: C++层 RLE 解码生成 RGBA 纹理，供 Flutter 渲染使用
- **落盘存储**: 保存为 SAM2 标准 JSON 格式，一帧包含多个 mask RLE，与官方数据集完全一致
- **压缩优势**: RLE 压缩比 5-20:1，2MB 空间可支持大量 mask 对象
- **实时性能**: RLE 解码速度 1-2ms，满足 30fps 要求
- **标准兼容**: 完全兼容 SAM2 和 pycocotools 生态

### 6.7 SAM2 标准存储格式示例

**与 SAM2 数据集完全一致的 JSON 格式**：

```json
{
  "frame_id": 12345,
  "timestamp": 1640995200000,
  "width": 1920,
  "height": 1080,
  "masklet": [
    {
      "size": [1080, 1920],
      "counts": "i\\Y4<Qj05K4L4M3M2N3O000010O00001O00010O00000O101O0000000000000O010000000O10O10000O01000O100O010O1O1O1N2O1O0O2O1N20O01O1001N101O1N..."
    },
    {
      "size": [1080, 1920],
      "counts": "Ynl18ni0?E8H8H7I7J6H8K5L3M4J6K5L4M2M4M3L4L4L4M3M3L4M3M3L4M2N3M3M3M4L5J<F?@<D5K4L4M2M3M3M3N2N..."
    }
  ]
}
```

**存储路径结构**：

```text
exports/
├── frames/
│   ├── frame_00001.json    # 第1帧的所有mask
│   ├── frame_00002.json    # 第2帧的所有mask
│   └── ...
└── metadata/
    └── session_info.json   # 会话元信息
```

**内存传递格式调整**：

```c
// 修改后的mask数据区域布局
typedef struct {
    MaskHeader header;           // mask头信息（兼容SAM2）
    MaskInfo masks[MAX_MASKS];   // 每个mask的信息数组（兼容SAM2）
    uint8_t rle_data[];             // 各个mask的RLE数据连续存储
} OptimizedMaskData;

// 单个mask的RLE信息
typedef struct {
    uint32_t object_id;               // mask唯一标识
    uint32_t rle_offset;            // RLE数据偏移量
    uint32_t rle_counts_length;     // counts数组长度
    uint32_t original_size;         // 原始数据大小(width*height)
    uint32_t tissue_id;            // 渲染颜色 RGBA格式
    uint8_t reserved[4];            // 字节对齐保留
} MaskInfo;
```

**性能优势分析**：

- **内存占用**: 原始 20MB → RLE 压缩后约 2-4MB (压缩比 5-10:1)
- **传输效率**: 减少 75-90%的数据传输量
- **解码速度**: RLE 解码 ≈ 1-2ms，满足 33ms/帧的实时要求
- **缓存成本**: 4 个缓冲区总计约 10-15MB，相比 80MB 显著降低

## 7. 总结

本设计方案通过共享内存和信号量机制实现了算法进程与 Flutter App 之间的高效数据传递，满足 30fps 实时性能要求。系统具有以下特点：

1. **高性能**: 零拷贝数据传递，环形缓冲区设计
2. **实时性**: 支持 30fps 帧率，非阻塞接口设计
3. **可靠性**: 完善的错误处理和资源管理机制
4. **扩展性**: 模块化设计，易于扩展新功能
5. **易用性**: 完整的 Dart API 封装，简化上层开发

系统已集成到现有的 medias_kit 插件架构中，充分利用了现有的 TextureRenderer 组件，确保了代码复用和维护性。
