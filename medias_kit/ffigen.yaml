# 仅对非 ffi-native 生效
name: MediasKitBindings
# 仅对非 ffi-native 生效
description: FFI bindings for MediasKit
# 基于 yaml 文件当前路径
output: ../lib/core/medias_kit_ffi.dart
ffi-native:
  # 作用指定动态库加载路径
  asset-id: package:medias_kit/core/medias_kit_ffi.dart
preamble: |
  // 功能: MediasKit FFI函数绑定
headers:
  entry-points:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/types/types_ffi.h
    - ../linux/include/private/types/vector_ffi.h
    - ../linux/include/private/device/device_ffi.h
    - ../linux/include/private/renderer/texture_renderer_ffi.h
    - ../linux/include/private/monitor/monitor_ffi.h
    - ../linux/include/private/recorder/recorder_ffi.h
    - ../linux/include/private/rtc_live/rtc_live_ffi.h
    - ../linux/include/private/rtmp_live/rtmp_live_ffi.h
    - ../linux/include/private/voice_helper/voice_helper_ffi.h
    - ../linux/include/private/helper/helper_ffi.h
    - ../linux/include/private/mask_ipc/mask_ipc_ffi.h
  include-directives:
    # 基于 yaml 文件当前路径
    - ../linux/include/private/types/types_ffi.h
    - ../linux/include/private/types/vector_ffi.h
    - ../linux/include/private/device/device_ffi.h
    - ../linux/include/private/renderer/texture_renderer_ffi.h
    - ../linux/include/private/monitor/monitor_ffi.h
    - ../linux/include/private/recorder/recorder_ffi.h
    - ../linux/include/private/rtc_live/rtc_live_ffi.h
    - ../linux/include/private/rtmp_live/rtmp_live_ffi.h
    - ../linux/include/private/voice_helper/voice_helper_ffi.h
    - ../linux/include/private/helper/helper_ffi.h
    - ../linux/include/private/mask_ipc/mask_ipc_ffi.h
compiler-opts:
  # 基于 `dart run ffigen --config <yaml-file-path>` 命令执行路径
  - -Ilinux/include/private
  - -I{FLUTTER_ROOT}/bin/cache/dart-sdk/include
# 忽略枚举警告, 因为枚举映射的是 int 类型, 宽度在不同平台不一致，可能导致内存安全问题
silence-enum-warning: true
enums:
  member-rename:
    # 匹配所有枚举
    "(.*)":
      # 匹配所有下划线分割的枚举成员, 移除前缀
      "(.*)_(.*)": "$2"
functions:
  # 表示函数不生成 dart 绑定函数
  exclude:
    - getDeviceIsOnline
  # 表示函数不在经过 dart runtime, 可直接直接通过地址调用 C, 快但不安全
  leaf:
    include:
      - setAudioSourceVolume
      - setAudioSinkVolume
      - setDefaultAudioSink
      - setDefaultAudioSource
      - monitorInit
      - recorderCreate
      - rtcLiveCreate
      - rtmpLiveCreate
  # 生成函数符号地址, 会生成变量 const addresses = _SymbolAddresses(); 并生成地址获取函数
  symbol-address:
    # 指定生成符号地址的函数
    include:
      - nativeMessageDestroyWithoutValuePtr
      - videoCaptureDeviceDestroy
      - audioDeviceDestroy
      - hostDeviceSpaceInfoDestroy
      - videoRecordInfoDestroy
      - rtcStatsDestroy
      - voiceCommandDestroy
      - copyFileInfoDestroy
      - transferDataDestroy
      - textureRendererRenderRgbaFrame
      - textureRendererRenderRgbaFrameWithMask
