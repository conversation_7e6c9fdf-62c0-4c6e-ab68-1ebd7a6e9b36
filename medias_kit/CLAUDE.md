# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Flutter FFI plugin called `medias_kit` that provides multimedia functionality with native implementations. The project integrates multiple SDKs including Agora RTC, XFYun speech services, Magewell capture hardware, and OpenCV for advanced video/audio processing.

## Development Commands

### Flutter Commands

```bash
# Get dependencies
flutter pub get

# Run code generation (FFI bindings)
./ffigen.sh

# Run tests
flutter test

# Analyze code
flutter analyze

# Run linting
dart fix --dry-run  # Check potential fixes
dart fix --apply    # Apply fixes
```

### Example App

```bash
cd example/
flutter pub get
./run.sh              # Run on Linux (default)
./run.sh --debug      # Run with debug build and lldb
./run.sh --release    # Run release build
./run.sh --verbose    # Run with verbose output
```

### Native Build (Linux)

```bash
cd example/
flutter build linux --debug    # Debug build
flutter build linux --release  # Release build
```

## Code Architecture

### Core Structure

- **lib/**: Dart API layer with platform interface implementations
- **linux/**: Native C++ implementation with FFI bindings
- **example/**: Demo app showcasing plugin functionality

### Key Components

#### Native Layer (linux/)

- **source/**: C++ implementation files organized by functionality
  - `codec/`: Audio/video encoding (H.264, AAC)
  - `stream&capture/`: Video capture and streaming
  - `renderer/`: Audio and texture rendering
  - `rtc_live/`: Agora RTC integration
  - `rtmp_live/`: RTMP streaming
  - `recorder/`: Media recording
  - `monitor/`: System monitoring
  - `peripheral/`: BLE peripheral management
  - `voice_helper/`: XFYun speech services
  - `mask_ipc/`: Real-time mask data IPC system

#### Dart Layer (lib/)

- **core/**: Platform interface and method channels
- **model/**: Media model and view components
- **rtc_live/**: RTC streaming API
- **rtmp_live/**: RTMP streaming API
- **recorder/**: Recording functionality
- **voice/**: Speech recognition/synthesis
- **ble/**: Bluetooth LE support
- **mask_ipc/**: Mask IPC communication API

### External Dependencies

#### Native SDKs (linux/dependencies/)

- **Agora SDK**: Real-time communication (libs: `agora-rtc-sdk`, `agora-fdkaac`, `aosl`)
- **XFYun SDK**: Speech services (libs: `aikit`, `msc`)
- **Magewell SDK**: Video capture hardware (libs: `MWCapture`, `mw_venc`, `mw_mp4`)
- **BlueZ**: Bluetooth LE support (lib: `Binc`)

#### System Libraries

- OpenCV for computer vision
- FFmpeg components: `avcodec`, `x264`, `fdk-aac`
- Audio: `asound`, `pulse`, `pulse-simple`
- Video: `v4l2`, `va-drm`, `epoxy`

### Build Configuration

The CMakeLists.txt uses:

- C++17 standard
- Flutter plugin architecture with shared library output
- Multiple SDK integration with proper include paths and linking
- Conditional test builds with GoogleTest (currently disabled)

### FFI Integration

The project uses `ffigen` to generate Dart-C bindings:

- Configuration in `ffigen.yaml`
- Generated bindings for native function calls  
- FFI headers in `linux/include/private/*/ffi.h`
- Run `./ffigen.sh` to regenerate bindings after C header changes

### Mask IPC System

New high-performance IPC system for real-time mask data transfer between Python algorithm processes and Flutter app:

- **Purpose**: 30fps real-time image and mask data transmission
- **Implementation**: Shared memory with zero-copy data transfer
- **Synchronization**: pthread mutexes and condition variables
- **Architecture**: Ring buffer with 4 frame buffers (42MB total)
- **Components**:
  - `lib/mask_ipc/`: Dart API for mask data reception
  - `linux/source/mask_ipc/`: C++ IPC implementation
  - `ipc_tool/`: Python shared memory client tools
  - `docs/algorithm_ipc_design.md`: Detailed system design

#### Usage Pattern

1. Algorithm process writes compressed RLE mask data to shared memory
2. App receives decoded mask frames via stream interface
3. Automatic frame processing acknowledgment and buffer management

### Environment Setup

When running example app, ensure proper environment:

```bash
# Required for XFYun SDK
export XF_ASSETS=/path/to/assets
export LD_LIBRARY_PATH=/path/to/linux/dependencies/xfyun_sdk/libs

# Required for Flutter engine logging
export FLUTTER_ENGINE_LOGGING=true
export DISPLAY=:0
```

### Asset Structure

Rich asset collection in `assets/`:

- **shaders/**: GLSL fragment shaders for effects (gaussian_blur.frag, pixelation.frag)
- **xfyun/**: XFYun speech model files and test audio
  - `aisound/`: TTS voice model files
  - `esr/`: Speech recognition models
  - `ivw70/`: Voice wake-up models and test audio
  - `xtts/`: Text-to-speech models
- **voice_words.txt**: Speech recognition vocabulary

## Testing

- Dart tests in `test/`
- Native C++ tests in `linux/test/` (GoogleTest framework)
- Integration tests in `example/integration_test/`

## Notes

- This is a Linux-focused plugin with heavy native integration
- Requires specific hardware support (Magewell capture cards)
- Uses multiple proprietary SDKs that may require licensing
- Build environment needs all SDK dependencies properly installed

## Development Workflow

### Adding New Features

1. For native functionality: Add C++ implementation in `linux/source/`
2. Create corresponding FFI headers in `linux/include/private/*/ffi.h`
3. Update `ffigen.yaml` to include new headers
4. Run `./ffigen.sh` to regenerate Dart bindings
5. Implement Dart API in `lib/`
6. Add example usage in `example/lib/src/modules/`

### IPC Development

When working with the Mask IPC system:

- Python algorithm side: Use `ipc_tool/shared_memory_client.py`
- Flutter app side: Use `lib/mask_ipc/mask_ipc.dart`
- Test IPC functionality in `example/lib/src/modules/ipc/`
- Refer to `docs/algorithm_ipc_design.md` for implementation details
